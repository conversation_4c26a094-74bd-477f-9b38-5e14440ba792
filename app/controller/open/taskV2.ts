'use strict';

import { Controller } from 'egg';
import validate from '../../core/decorators/validate';
import baseError from '../../core/base/baseError';
import { ETaskResourceType } from '../../model/task';
import * as uuid from 'uuid/v4';
// import axios from 'axios';
import { htmlToJsonV5 } from '../../core/utils/aiEdit/htmlToJsonAI';
// @ts-ignore
import { iterateNode, json2Jpg } from '../../core/utils/treeHelper';
import { ITaskFiles } from '../../model/taskFile';

export default class OpenTaskV2Controller extends Controller {

  // word 预处理完成，更新结果
  @validate({
    taskId: 'string',
    status: 'number',
    type: 'string',
    reason: 'string?',
  })
  public async updateTaskResult() {
    const { ctx, service, logger, config } = this;
    const { statuses } = service.task.base;
    const { taskId: id, status, reason, type } = ctx.input as {
      taskId: string;
      type: 'word' | 'fbd';
      status: 0 | 1;
      reason?: string;
    };
    const idSplitArr = id.split('.').map((item, index) => (index === 0 ? Number(item) : item));
    const taskId = idSplitArr.splice(0, 1).join('.');
    const fileId = idSplitArr.join('.');
    logger.info(`open.taskV2.updateTaskResult.input: taskId - ${id}, type - ${type}, status - ${status}`);
    if (type !== 'word' && type !== 'fbd') {
      return ctx.body = baseError.paramsError('任务类型错误');
    }
    const task = await service.task.base.getOne({
      where: { taskId, resourceType: type === 'word' ? ETaskResourceType.WORDV2 : ETaskResourceType.FBDV2 },
      attributes: ['appKey', 'subject', 'status', 'taskId'],
    });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if (task.status > statuses.unmarked) {
      return ctx.body = baseError.dataAlreadyExistError('任务已进入后续流程');
    }
    const file = await service.task.file.getFiles({ taskId });
    // @todo：若发现任务没有正常保存 file 数据，则直接抛出异常
    if (!Object.keys(file).length) {
      await service.task.base.update({
        status: statuses.error,
        errorInfo: 'word 文件预处理完成，更新结果时发现：任务包含的 file 信息不存在',
      }, { where: { taskId } });
      return ctx.body = baseError.dataNotExistError('任务包含的 file 信息不存在');
    }
    if (status === 1) {
      logger.error(`open.taskV2.updateTaskResult.status === 1：${reason}`);
      await service.task.base.update({
        status: statuses.error,
        errorInfo: `${type}预处理异常${reason || ''}`,
      }, { where: { taskId } });
      // word/fbd 预处理异常，通知 lark 机器人
      const errorData = {
        type: 'error',
        receive_id: 13,
        server: { name: 'xdoc线上服务' },
        content: {
          title: `${type}预处理异常`,
          text: `open.taskV2.updateTaskResult---statusError---taskId---> ${taskId}
          errorInfo: ${reason || ''}
          `,
        },
      };
      service.robot.sendRobotMessage(errorData);
      if (type === 'fbd') {
        const zips = file.zips!;
        zips.forEach((zip) => {
          if (zip.id === fileId) {
            zip.isError = true;
          }
        });
        await service.task.file.setFiles([task.taskId], { zips });
      } else {
        const words = file.words!;
        const wordsArr = [...words.body, ...words.answer];
        wordsArr.forEach((word) => {
          if (word.id === fileId) {
            word.isError = true;
          }
        });
        await service.task.file.setFiles([task.taskId], { words });
      }
      return ctx.body = { status: 0 };
    }
    let down = true;
    if (type === 'fbd') {
      const zips = file.zips!;
      // !! bug 发起任务是按 fbd 发起的， 但是回调是按照 zip 来判断是否完成的！
      zips.forEach((zip) => {
        if (zip.id === fileId) {
          zip.isDown = true;
        }
      });
      await service.task.file.setFiles([task.taskId], { zips });
      if (zips.some((zip) => !zip.isDown)) down = false;
    } else {
      const words = file.words!;
      const wordsArr = [...words.body, ...words.answer];
      wordsArr.forEach((word) => {
        if (word.id === fileId) {
          word.isDown = true;
        }
      });
      await service.task.file.setFiles([task.taskId], { words });
      if (wordsArr.some((word) => !word.isDown)) down = false;
    }
    if (down) {
      await service.task.base.update({
        status: statuses.updating,
        splitUserId: 0,
      }, { where: { taskId } });
    }
    ctx.runInBackground(async() => {
      try {
        if (down) {
          const files = type === 'fbd' ? file.fbds! : file.words!;
          const fileIds: string[] = [];
          Object.values(files).forEach((v) => v.forEach((item) => fileIds.push(item.id)));
          await service.task.taskV2.mergePreHandleHtml(task.appKey, task.taskId, files);
          /*
           * const html = await service.task.taskV2.mergePreHandleHtml(task.appKey, task.taskId, files);
           * const docKey = service.task.base.getOssKey(task.appKey, taskId, 'machine.docx');
           * await service.oss.convertHtmlToDocAndUpload(docKey, html, true);
           */
          const meta = await service.task.meta.getMetas({ taskId });
          const isAIEdit = meta.isAIEdit;
          const isAIAuto = meta.isAIAuto;

          if (isAIEdit && isAIAuto) {
            await service.task.base.update(
              { status: statuses.htmlHeaderFixProcessing },
              { where: { taskId } }
            );
            // 入队
            const htmlUrl = service.task.base.getUrl(task.appKey, task.taskId, 'html');
            await service.rbs.initRBSQueue({
              task_id: task.taskId.toString(),
              task_type: 'title_level',
              task_info: {
                task_id: task.taskId.toString(),
                html_url: htmlUrl,
                bucket_name: config.aliOss.bucket,
                is_ai_edit: meta.isAIEdit,
                app_key: task.appKey,
                subject: meta?.subject || task.subject,
                upload_path: service.task.base.getOssKey(task.appKey, task.taskId, 'header_fixed.html'),
                get_task_parameters: [
                  'task_id',
                  'html_url',
                  'is_ai_edit',
                  'subject',
                  'app_key',
                  'bucket_name',
                  'upload_path'
                ],
                callback_extras: [
                  'task_id',
                  'html_url',
                  'subject',
                  'app_key',
                  'bucket_name',
                  'upload_path'
                ],
                run_type: 'common',
                callback_url: `${config.ngrok.callbackUrl}/api/open/task/updateHtmlHeaderFix`,
                push_time: new Date().getTime(),
                timestamp: new Date().getTime(),
              },
            });
          } else {
            await service.task.base.update(
              { status: statuses.split },
              { where: { taskId } }
            );
          }
        }
      } catch (e) {
        logger.error(`open.taskV2.updateTaskResult.runInBackground.catch:${e}`);
        await service.task.base.update({
          status: statuses.error,
          errorInfo: (e as any).message || '任务预处理结果更新错误',
        }, { where: { taskId } });
      }
    });
    ctx.body = { status: 0 };
  }

  public async updateTaskResultWordNew() {
    const { ctx, service, logger, config } = this;
    const { statuses } = service.task.base;
    // 使用数据库中的 runAiFixed 字段，而不是传入的参数
    // 如果没有这个参数，就认为是自动进来的，需要走修复
    // 如果有这个参数并且为 false，就认为是手动进来的，不需要走修复
    const { data: { task_id: _id1, taskid: _id2, status, reason, type } } = ctx.input as {
      data: {
        task_id: string;
        taskid: string;
        type: 'word' | 'fbd';
        status: 0 | 1;
        reason?: string;
      }
    };
    const id = _id1 || _id2;
    logger.info(`updateTaskResultWordNew input: ${JSON.stringify(ctx.input)}`);
    const idSplitArr = id.split('.').map((item, index) => (index === 0 ? Number(item) : item));
    const taskId = idSplitArr.splice(0, 1).join('.');
    const fileId = idSplitArr.join('.');
    logger.info(`open.taskV2.updateTaskResult.input: taskId - ${id}, type - ${type}, status - ${status}`);
    if (type !== 'word' && type !== 'fbd') {
      return ctx.body = baseError.paramsError('任务类型错误');
    }
    const task = await service.task.base.getOne({
      where: {
        taskId,
        // resourceType: type === 'word' ? ETaskResourceType.WORDV2 : ETaskResourceType.FBDV2
      },
      attributes: ['appKey', 'subject', 'status', 'taskId', 'resourceType'],
    });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }
    if (task.status > statuses.unmarked) {
      return ctx.body = baseError.dataAlreadyExistError('任务已进入后续流程');
    }
    // 从task_meta中获取runAiFixed值
    const meta = await service.task.meta.getMetas({ taskId });
    // 如果meta中没有这个字段，或者存在这个字段并且值为true时，结果为true
    const runAiFixed = meta.runAiFixed !== false;

    const file = await service.task.file.getFiles({ taskId });
    // @todo：若发现任务没有正常保存 file 数据，则直接抛出异常
    if (!Object.keys(file).length) {
      await service.task.base.update({
        status: statuses.error,
        errorInfo: 'word 文件预处理完成，更新结果时发现：任务包含的 file 信息不存在',
      }, { where: { taskId } });
      return ctx.body = baseError.dataNotExistError('任务包含的 file 信息不存在');
    }
    if (status === 1) {
      logger.error(`open.taskV2.updateTaskResult.status === 1：${reason}`);
      await service.task.base.update({
        status: statuses.error,
        errorInfo: `${type}预处理异常${reason || ''}`,
      }, { where: { taskId } });
      // word / fbd 预处理异常，通知lark机器人
      const errorData = {
        type: 'error',
        receive_id: 13,
        server: { name: 'xdoc线上服务' },
        content: {
          title: `${type}预处理异常`,
          text: `open.taskV2.updateTaskResult---statusError---taskId---> ${taskId}
          errorInfo: ${reason || ''}
          `,
        },
      };
      service.robot.sendRobotMessage(errorData);
      if (type === 'fbd') {
        const zips = file.zips!;
        zips.forEach((zip) => {
          if (zip.id === fileId) {
            zip.isError = true;
          }
        });
        await service.task.file.setFiles([task.taskId], { zips });
      } else {
        const words = file.words!;
        const wordsArr = [...words.body, ...words.answer];
        wordsArr.forEach((word) => {
          if (word.id === fileId) {
            word.isError = true;
          }
        });
        await service.task.file.setFiles([task.taskId], { words });
      }
      return ctx.body = { status: 0 };
    }

    logger.info(`updateTaskResultWordNew step1: ${JSON.stringify(file)}}`);

    let down = true;
    if (type === 'fbd') {
      const zips = file.zips!;
      zips.forEach((zip) => {
        if (zip.id === fileId) {
          zip.isDown = true;
        }
      });
      await service.task.file.setFiles([task.taskId], { zips });
      if (zips.some((zip) => !zip.isDown)) down = false;
    } else {
      const words = file.words!;
      const wordsArr = [...words.body, ...words.answer];
      wordsArr.forEach((word) => {
        if (word.id === fileId) {
          word.isDown = true;
        }
      });
      await service.task.file.setFiles([task.taskId], { words });
      if (wordsArr.some((word) => !word.isDown)) down = false;
    }
    if (down) {
      await service.task.base.update({
        status: statuses.updating,
        splitUserId: 0,
      }, { where: { taskId } });
    }
    ctx.runInBackground(async() => {
      try {
        if (down) {
          const files = type === 'fbd' ? file.fbds! : file.words!;
          const fileIds: string[] = [];
          Object.values(files).forEach((v) => v.forEach((item) => fileIds.push(item.id)));
          await service.task.taskV2.mergePreHandleHtml(task.appKey, task.taskId, files);
          let htmlUrl = service.task.base.getUrl(task.appKey, task.taskId, 'machine.html', true, false);
          // const meta = await service.task.meta.getMetas({ taskId });
          // 进入 html 修复流程【这个流程只有 九学王 - word + ai 编辑项目 有】
          if ((['d45dcb5325227a23b421cdeb', 'fc7539b21810cd4f0f0fb620'].includes(task.appKey) && type === 'word' && runAiFixed) || meta.isAIEdit) {
            logger.info(`open.taskV2.updateTaskResult.htmlFixProcessing: ${taskId}`);
            // 如果是 ai 编辑项目，需要先 clean 一下，去掉标记
            if (meta.isAIEdit) {
              const html = await service.ai.httpGetWithRetry(htmlUrl);
              const cleanHtml = service.ai.cleanHTMLSymbol(html.data);
              const htmlKey = service.task.base.getOssKey(task.appKey, task.taskId, 'html');
              const cleanedHtmlKey = service.task.base.getOssKey(task.appKey, task.taskId, 'backup.html');
              // const backupKey = service.task.base.getOssKey(task.appKey, task.taskId, 'backup.html');
              // 上传到 oss
              await service.oss.upload(cleanedHtmlKey, cleanHtml, 'string');
              await service.oss.upload(htmlKey, cleanHtml, 'string');
              // const ossClient = service.oss.createOss();
              // copy 到 backup.html
              // try {
              //   await ossClient.copy(htmlKey, backupKey);
              //   logger.info('open.taskV2.updateTaskResult.htmlFixProcessing.copy success');
              // } catch (e) {
              //   logger.info(`open.taskV2.updateTaskResult.htmlFixProcessing.copy error: ${e.message}`);
              // }
              htmlUrl = service.task.base.getUrl(task.appKey, task.taskId, 'html');
            }
            // 进入HTML修复流程
            await service.rbs.initRBSQueue({
              task_id: task.taskId.toString(),
              task_type: 'title_level',
              task_info: {
                task_id: task.taskId.toString(),
                html_url: htmlUrl,
                subject: meta?.subject || task.subject,
                app_key: task.appKey,
                is_ai_edit: meta.isAIEdit,
                bucket_name: config.aliOss.bucket,
                upload_path: service.task.base.getOssKey(task.appKey, task.taskId, 'header_fixed.html'),
                get_task_parameters: [
                  'task_id',
                  'html_url',
                  'is_ai_edit',
                  'subject',
                  'app_key',
                  'bucket_name',
                  'upload_path'
                ],
                callback_extras: [
                  'task_id',
                  'html_url',
                  'subject',
                  'app_key',
                  'bucket_name',
                  'upload_path'
                ],
                run_type: 'common',
                callback_url: `${config.ngrok.callbackUrl}/api/open/task/updateHtmlHeaderFix`,
                push_time: new Date().getTime(),
                timestamp: new Date().getTime(),
              },
            });
            if (meta.isAIEdit) {
              const taskParams = service.ai.formatTaskParams({
                taskId,
                appKey: task.appKey,
                subject: meta?.subject || task.subject,
              });

              await service.timeout.addTimeoutTask(
                taskId,
                'title_level',
                3 * 60 * 1000,
                {
                  title: 'title_level任务超时',
                  content: `任务ID: ${taskId} 的title_level任务执行超时，请检查title_level服务状态`,
                  receiveId: 4,
                  extra: taskParams,
                }
              );
            }
            logger.info(`open.taskV2.updateTaskResult.htmlFixProcessing: ${taskId}`);
            await service.task.base.update(
              { status: statuses.htmlHeaderFixProcessing },
              { where: { taskId } }
            );
          } else {
            const resourceType = task.resourceType;
            if (resourceType === ETaskResourceType.IMAGE) {
              // doc2x 新流程，图片任务转成 word 任务
              await service.task.base.update({ resourceType: ETaskResourceType.WORDV2 }, { where: { taskId } });
            } else {
              await service.task.base.update(
                { status: statuses.split },
                { where: { taskId } }
              );
            }
          }
          /*
           * const html = await service.task.taskV2.mergePreHandleHtml(task.appKey, task.taskId, files);
           * const docKey = service.task.base.getOssKey(task.appKey, taskId, 'machine.docx');
           * await service.oss.convertHtmlToDocAndUpload(docKey, html, true);
           */
        }
      } catch (e) {
        logger.error(`open.taskV2.updateTaskResult.runInBackground.catch:${e}`);
        await service.task.base.update({
          status: statuses.error,
          errorInfo: (e as any).message || '任务预处理结果更新错误',
        }, { where: { taskId } });
      }
    });
    ctx.body = { status: 0 };
  }

  @validate({
    pdf: {
      type: 'object',
      rule: {
        taskId: 'string',
        id: 'string',
      },
    },
    pdf_count: 'number?',
  })
  public async updateFile() {
    const { ctx, service, app } = this;
    const { pdf, pdf_count } = ctx.input as {
      pdf: { taskId: string; id: string; url: string; };
      pdf_count: number;
    };
    // 注意这里传入的 id 是 md5
    const { taskId: zipId, id } = pdf;
    const [taskId, wordId] = zipId.split('.');
    if (!taskId || !wordId) {
      return ctx.body = baseError.dataNotExistError('任务ID异常');
    }
    const task = service.task.base.getOne({ where: { taskId: Number(taskId) } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('没找到对应任务');
    }
    // 这里 pdfs 里的 id 也是 md5 , 数据库里key-value的格式. 不是主键
    const file = await service.task.file.getFiles({ taskId });
    const pdfs = file.pdfs || [];
    if (!file.words) {
      return ctx.body = baseError.dataNotExistError('没找到word');
    }
    let wordName = '';
    try {
      Object.values(file.words!).forEach((item) => item.forEach((item) => {
        if (item.id === wordId) {
          wordName = item.name;
        }
      }));
    } catch (_) {
      return ctx.body = baseError.serverError('解析file.words出现异常');
    }

    if (id && wordName) {
      const pdf = pdfs.find((item) => item.id === id || item.name === wordName);
      this.logger.info(`updateFile-pdfs: ${JSON.stringify(pdfs)}`, 'id-', id, 'wordName-', wordName);
      this.logger.info(`updateFile-pdf: ${JSON.stringify(pdf)}`);
      if (!pdf) {
        const newPdf = { id, name: wordName, url: `https://hexin-worksheet.oss-cn-shanghai.aliyuncs.com${app.config.workOrder.fileSysPath}/${id}.pdf` };
        await service.task.taskV2.pdf2image([newPdf]);
        pdfs.push(newPdf);
      }
      await service.task.file.setFiles([Number(taskId)], { pdfs });
    } else {
      return ctx.body = baseError.dataNotExistError(`id: ${id}, name: ${wordName}`);
    }

    // 页数同步到工单
    if (pdf_count) {
      // 通过taskId获取工单id
      const task = await service.task.taskV2.getOneById(Number(taskId));
      if (task && task.bookId) {
        const book = await service.book.getOne({ where: { id: task.bookId } });
        if (book && book.projectId) {
          const project = await service.project.base.getOne({ where: { id: book.projectId } });
          if (project && project.workOrder) {
            // 通过 project.workOrder + wordId + pdf_count 更新工单
            this.logger.info(`updatePdfCount begin taskId:${taskId} workOrder:${project.workOrder}, wordId:${wordId}, pdf_count:${pdf_count}`);
            await app.curl(`${this.config.workOrder.api}/api/open/ticket/v1/handle`, {
              method: 'PUT',
              dataType: 'json',
              data: JSON.stringify({
                ticket_id: project.workOrder,
                word_page_count: pdf_count,
                word_md5: wordId,
              }),
            });
          }
        }
      }
    }
    ctx.body = { status: 0 };
  }

  public async test() {
    const { service, ctx } = this;
    const { appKey, taskId, extension } = ctx.input as {
      appKey: string;
      taskId: number;
      extension: 'html'
    };
    const isExist = await service.task.base.hasOssData(appKey, taskId, extension);
    ctx.body = {
      isExist,
      status: 0,
    };
  }

  @validate({ jsonUrl: 'string' })
  public async validateJson() {
    const { service, ctx, logger } = this;
    const { jsonUrl } = ctx.input as {
      jsonUrl: string;
    };
    logger.info(`validateJson begin jsonUrl:${jsonUrl}`);
    const json = await service.oss.fetch(jsonUrl, 'json');
    if (!json) {
      logger.info(`validateJson json dont exist ${jsonUrl}`);
      return ctx.body = baseError.dataNotExistError('json不存在');
    }

    try {
      const { validatedJson, isError } = service.task.taskV2.getValidateJson(json);
      if (isError) {
        // 上传json到工单
        const uid = uuid().replace(/-/g, '').substring(0, 20);
        let checkUrl = await service.oss.upload(`check/${uid}.checked.json`, validatedJson, 'string');
        checkUrl = (checkUrl as string).replace('-internal', '');
        logger.info(`validateJson success url : check/${uid}.checked.json`);
        return ctx.body = {
          checkUrl,
          status: 0,
        };
      }
    } catch (e) {
      logger.info(`validateJson json convert json failed ${jsonUrl}`);
      return ctx.body = baseError.serverError('提取json error异常');
    }
    ctx.body = { status: 0 };
  }

  /**
   * 处理HTML修复回调
   */
  public async updateHtmlFix() {
    const { ctx, service, logger } = this;
    const { statuses } = service.task.base;

    // 解析回调数据
    logger.info(`[updateHtmlFix] input: ${JSON.stringify(ctx.input)}`);
    const { task_id, status, error, result, stat } = (ctx.input?.data || {}) as {
      task_id: string;
      // 0 成功 1 失败
      status: number;
      // 如果报错，这里存储错误信息
      error: string;
      // 上传的路径
      result: string;
      cost_token: number;
      // 统计数据
      stat: any;
    };
    // 获取任务
    const taskId = task_id;
    const task = await service.task.base.getOne({
      where: { taskId },
      attributes: ['appKey', 'subject', 'status', 'taskId', 'bookId', 'resourceType'],
    });

    if (!task) {
      logger.error(`[updateHtmlFix] 任务不存在: ${taskId}`);
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }

    if (task.status !== statuses.htmlFixProcessing) {
      logger.error(`[updateHtmlFix] 任务状态异常: ${task.status}`);
      return ctx.body = baseError.dataAlreadyExistError('任务状态异常');
    }

    // 统计数据记录
    if (stat) {
      logger.info(`[updateHtmlFix] 统计数据: ${JSON.stringify(stat)}`);
      const book = await service.book.getOne({ where: { id: task.bookId } });
      if (book && book.projectId) {
        const project = await service.project.base.getOne({ where: { id: book.projectId } });
        if (project?.workOrder) {
          // 更新项目状态
          await service.ai.callbackTokens(project.workOrder, 'html_fix', stat.total.cost_token, stat);
        }
      }
    }
    // 释放
    await service.timeout.releaseTimeoutTask(taskId, 'html_fix');

    // 根据回调结果更新任务状态
    if (status === 1) {
      logger.error(`[updateHtmlFix] 修复失败: ${JSON.stringify(error)}`);

      // 发送错误通知
      const errorData = {
        type: 'error',
        receive_id: 13,
        server: { name: 'xdoc线上服务' },
        content: {
          title: 'HTML修复失败',
          text: `[updateHtmlFix] taskId: ${taskId}\nerrorInfo: ${error || '未知错误'}`,
        },
      };
      service.robot.sendRobotMessage(errorData);

      // 更新任务状态为错误
      await service.task.base.update({
        status: statuses.error,
        errorInfo: `HTML修复失败: ${error || '未知错误'}`,
      }, { where: { taskId } });
      return { status: 0 };
    }
    logger.info(`[updateHtmlFix] 修复成功: ${taskId}`);
    if (result) {
      try {
        // 获取OSS客户端
        const ossClient = service.oss.createOss();

        // 目标路径（HTML文件路径）
        const htmlKey = service.task.base.getOssKey(task.appKey, taskId, 'html');

        // 将fixed.html复制到html
        await ossClient.copy(htmlKey, result);

        logger.info(`[updateHtmlFix] 复制文件成功: 从 ${result} 到 ${htmlKey}`);

        // 获取任务元数据
        const meta = await service.task.meta.getMetas({ taskId });
        const isAIEdit = meta.isAIEdit;
        const isAIAuto = meta.isAIAuto;
        // 判断是否需要进入下一步流程
        if (isAIEdit && isAIAuto) {
          // 直接转 json，然后更新 project 状态，更新 task 状态，然后入队 ai_edit
          logger.info(`[updateHtmlFix] 直接转 json，更新 project 状态，更新 task 状态，入队 ai_edit: ${taskId}`);
          try {
            // 获取HTML内容
            const htmlUrl = service.task.base.getUrl(task.appKey, taskId, 'html');
            const htmlRes = await service.ai.httpGetWithRetry(htmlUrl);
            const html = htmlRes.data;

            // 转换为JSON
            let json: any[] = htmlToJsonV5({ html });
            logger.info(`[updateHtmlFix] 转 json 成功: ${taskId}`);

            // 处理JSON结构
            json = await service.ai.formatJSON(json);
            logger.info(`[updateHtmlFix] json 结构化问题处理成功: ${taskId}`);

            // 转JPG
            await json2Jpg(json);
            logger.info(`[updateHtmlFix] json 转图成功: ${taskId}`);

            // 保存JSON
            const jsonKey = service.task.base.getOssKey(task.appKey, taskId, 'json');
            await service.oss.upload(jsonKey, JSON.stringify(json), 'string');
            logger.info(`[updateHtmlFix] 保存 json 成功: ${taskId}`);

            // 更新任务状态
            await service.task.base.update({ status: statuses.reviewed }, { where: { taskId } });

            // 获取项目信息
            const book = await service.book.getOne({ where: { id: task.bookId } });
            if (book && book.projectId) {
              const project = await service.project.base.getOne({ where: { id: book.projectId } });
              if (project) {
                // 更新项目状态
                await service.project.base.update({ status: service.project.base.statuses.reviewed }, { where: { id: project.id } });
              }
            }

            // @todo：入队 AI 编辑
            // await service.rbs.initRBSQueue({
            //   task_id: taskId.toString(),
            //   task_type: 'ai_edit',
            //   task_info: {
            //     task_id: taskId.toString(),
            //     json_url: service.task.base.getUrl(task.appKey, taskId, 'json'),
            //     subject: meta?.subject || task.subject,
            //     app_key: task.appKey,
            //     bucket_name: ctx.app.config.aliOss.bucket,
            //     upload_path: service.task.base.getOssKey(task.appKey, taskId, 'edited.json'),
            //     get_task_parameters: [
            //       'task_id',
            //       'json_url',
            //       'subject',
            //       'app_key',
            //       'bucket_name',
            //       'upload_path'
            //     ],
            //     callback_extras: [
            //       'task_id',
            //       'json_url',
            //       'subject',
            //       'app_key',
            //       'bucket_name',
            //       'upload_path'
            //     ],
            //     run_type: 'common',
            //     callback_url: 'http://xdoc.open.hexinedu.com/api/open/task/updateAiEdit',
            //     push_time: new Date().getTime(),
            //     timestamp: new Date().getTime(),
            //   },
            // });

            logger.info(`[updateHtmlFix] 入队 AI 编辑成功: ${taskId}`);
          } catch (processError) {
            logger.error(`[updateHtmlFix] 处理JSON失败: ${processError}`);

            // 发送错误通知
            const errorData = {
              type: 'error',
              receive_id: 13,
              server: { name: 'xdoc线上服务' },
              content: {
                title: 'JSON处理失败',
                text: `[updateHtmlFix] taskId: ${taskId}\nJSON处理失败\nerrorInfo: ${processError}`,
              },
            };
            service.robot.sendRobotMessage(errorData);

            // 更新任务状态为错误
            await service.task.base.update({
              status: statuses.error,
              errorInfo: `JSON处理失败: ${processError}`,
            }, { where: { taskId } });
            // 返回成功状态，但实际上已经处理失败
            return;
          }
        } else {
          // 如果需要进入人工介入 AI 编辑流程，直接更新任务状态为拆分状态
          logger.info(`[updateHtmlFix] 任务元数据 isAIEdit: ${isAIEdit}, 需要进入人工介入 AI 编辑流程`);
        }

        // 更新任务状态为拆分状态
        await service.task.base.update({ status: statuses.split }, { where: { taskId } });
        const appKey = task.appKey;
        let canAutoPublishInJXW = false;
        // const subject = meta.subject || '';
        if (
          ['fc7539b21810cd4f0f0fb620', 'd45dcb5325227a23b421cdeb'].includes(appKey)
          // && ['history', 'daode_fazhi', 'geography', 'politics'].includes(subject)
        ) {
          const html = await service.task.base.getOssData(appKey, taskId, 'fixed.html');
          // 如果所有 p 的属性上都有data-check="ai_check"，则可以自动发布
          // const pTags = html.match(/<p[^>]*>/g);
          // 还需要转一下 json，收集所有 errorInfo ，个数 < 3 时，自动过
          // const hasErrorAiCheck = pTags.every((p: string) => p.includes('data-check="ai_check"'));
          const json = htmlToJsonV5({ html, from: 'open' });
          const errorInfo: any[] = [];
          for (const { node } of iterateNode(json)) {
            if (node.errorInfo && node.errorInfo.length > 0) {
              errorInfo.push(...node.errorInfo.filter((item: any) => item.type === 'error'));
            }
          }
          canAutoPublishInJXW = errorInfo.length <= 8;
          if (canAutoPublishInJXW) {
            canAutoPublishInJXW = true;
          }
        }
        // isAutoPublish 如果没这个字段，认为是第一次跑，设置成 true 就行
        const isAutoPublish = meta.isAutoPublish === undefined ? true : meta.isAutoPublish;
        if ((isAIEdit || canAutoPublishInJXW) && isAutoPublish) {
          try {
            await service.ai.autoPublishTask(task);
            await service.task.meta.setMetas([Number(taskId)], { isAutoPublish: true });
          } catch (e) {
            logger.error(`[updateHtmlFix] 自动发布任务失败: ${e}`);
            // 发送错误通知
            const errorData = {
              type: 'error',
              receive_id: 4,
              server: { name: 'xdoc线上服务' },
              content: {
                title: '自动发布任务失败',
                text: `[updateHtmlFix] taskId: ${taskId}\n自动发布任务失败\nerrorInfo: ${e}`,
              },
            };
            service.robot.sendRobotMessage(errorData);
          }
        }
      } catch (copyError) {
        logger.error(`[updateHtmlFix] 复制文件失败: ${copyError}`);

        // 发送错误通知
        const errorData = {
          type: 'error',
          receive_id: 4,
          server: { name: 'xdoc线上服务' },
          content: {
            title: 'HTML复制失败',
            text: `[updateHtmlFix] taskId: ${taskId}\n从 ${result} 复制到 html 失败\nerrorInfo: ${copyError}`,
          },
        };
        service.robot.sendRobotMessage(errorData);

        // 更新任务状态为错误
        await service.task.base.update({
          status: statuses.error,
          errorInfo: `HTML复制失败: ${copyError}`,
        }, { where: { taskId } });
      }
    } else {
      logger.error('[updateHtmlFix] 修复成功但没有返回result路径');

      // 获取任务元数据
      const meta = await service.task.meta.getMetas({ taskId });
      const isAIEdit = meta.isAIEdit;
      const isAIAuto = meta.isAIAuto;

      // 判断是否需要进入下一步流程
      if (isAIEdit && isAIAuto) {
        // 这里可以添加自定义逻辑，如果需要的话
        // 插槽位置，可以根据需求添加自定义逻辑
        logger.info(`[updateHtmlFix] 任务元数据 isAIEdit=${isAIEdit}, isAIAuto=${isAIAuto}`);
      }

      // 更新任务状态为拆分状态（尽管没有result，但状态为成功，所以继续流程）
      await service.task.base.update({ status: statuses.split }, { where: { taskId } });
    }

    ctx.body = { status: 0 };
  }

  /**
   * 处理HTML标题修复回调
   */
  public async updateHtmlHeaderFix() {
    const { ctx, service, logger, config } = this;
    const { statuses } = service.task.base;

    // 解析回调数据
    logger.info(`[updateHtmlHeaderFix] input: ${JSON.stringify(ctx.input)}`);
    const { task_id, status, error, result, stat } = (ctx.input?.data || {}) as {
      task_id: string;
      // 0 成功 1 失败
      status: number;
      // 如果报错，这里存储错误信息
      error: string;
      // 上传的路径
      result: string;
      cost_token: number;
      // 统计数据
      stat: any;
    };
    // 获取任务
    const taskId = task_id;
    const task = await service.task.base.getOne({
      where: { taskId },
      attributes: ['appKey', 'subject', 'status', 'taskId'],
    });

    if (!task) {
      logger.error(`[updateHtmlHeaderFix] 任务不存在: ${taskId}`);
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }

    if (task.status !== statuses.htmlHeaderFixProcessing) {
      logger.error(`[updateHtmlHeaderFix] 任务状态异常: ${task.status}`);
      return ctx.body = baseError.dataAlreadyExistError('任务状态异常');
    }
    // 释放
    await service.timeout.releaseTimeoutTask(taskId, 'title_level');

    // 统计数据记录
    if (stat) {
      logger.info(`[updateHtmlHeaderFix] 统计数据: ${JSON.stringify(stat)}`);
      const book = await service.book.getOne({ where: { id: task.bookId } });
      if (book && book.projectId) {
        const project = await service.project.base.getOne({ where: { id: book.projectId } });
        if (project?.workOrder) {
          // 更新项目状态
          await service.ai.callbackTokens(project.workOrder, 'title_level', stat.total.cost_token, stat);
        }
      }
    }

    // 根据回调结果更新任务状态
    if (status === 1) {
      logger.error(`[updateHtmlHeaderFix] 修复失败: ${JSON.stringify(error)}`);

      // 发送错误通知
      const errorData = {
        type: 'error',
        receive_id: 25,
        server: { name: 'xdoc线上服务' },
        content: {
          title: 'HTML标题修复失败',
          text: `[updateHtmlHeaderFix] taskId: ${taskId}\nerrorInfo: ${JSON.stringify(error) || '未知错误'}`,
        },
      };
      service.robot.sendRobotMessage(errorData);

      // 更新任务状态为错误
      await service.task.base.update({
        status: statuses.error,
        errorInfo: `HTML标题修复失败: ${error || '未知错误'}`,
      }, { where: { taskId } });
      return { status: 0 };
    }
    logger.info(`[updateHtmlHeaderFix] 修复成功: ${taskId}`);

    if (result) {
      try {
        // 获取OSS客户端
        const ossClient = service.oss.createOss();

        // 目标路径（HTML文件路径）
        const htmlKey = service.task.base.getOssKey(task.appKey, taskId, 'html');

        // 将header_fixed.html复制到html
        await ossClient.copy(htmlKey, result);

        logger.info(`[updateHtmlHeaderFix] 复制文件成功: 从 ${result} 到 ${htmlKey}`);

        // 获取任务元数据
        const meta = await service.task.meta.getMetas({ taskId });
        const isAIEdit = meta.isAIEdit;
        // const isAIAuto = meta.isAIAuto;

        // 判断是否需要进入HTML修复流程
        if (isAIEdit || 1) {
          // 更新任务状态为 HTML 修复处理中
          await service.task.base.update(
            { status: statuses.htmlFixProcessing },
            { where: { taskId } }
          );

          // 入队进行 HTML 修复
          const htmlUrl = service.task.base.getUrl(task.appKey, taskId, 'html');
          await service.rbs.initRBSQueue({
            task_id: task.taskId.toString(),
            task_type: isAIEdit ? 'xdoc_html_fix_priority' : 'xdoc_html_auto_fix',
            task_info: {
              task_id: task.taskId.toString(),
              html_url: htmlUrl,
              subject: meta?.subject || task.subject,
              app_key: task.appKey,
              is_ai_edit: isAIEdit,
              bucket_name: config.aliOss.bucket,
              upload_path: service.task.base.getOssKey(task.appKey, taskId, 'fixed.html'),
              get_task_parameters: [
                'task_id',
                'html_url',
                'subject',
                'app_key',
                'is_ai_edit',
                'bucket_name',
                'upload_path'
              ],
              callback_extras: [
                'task_id',
                'html_url',
                'subject',
                'app_key',
                'bucket_name',
                'upload_path'
              ],
              run_type: 'common',
              callback_url: `${config.ngrok.callbackUrl}/api/open/task/updateHtmlFix`,
              push_time: new Date().getTime(),
              timestamp: new Date().getTime(),
            },
          });
          // 监控
          if (isAIEdit) {
            const taskParams = service.ai.formatTaskParams({
              taskId,
              appKey: task.appKey,
            });

            await service.timeout.addTimeoutTask(
              taskId,
              'html_fix',
              5 * 60 * 1000,
              {
                title: 'html_fix任务超时',
                content: `任务ID: ${taskId} 的html_fix任务执行超时，请检查html_fix服务状态`,
                receiveId: 4,
                extra: taskParams,
              }
            );
          }

          logger.info(`[updateHtmlHeaderFix] 入队 HTML 修复成功: ${taskId}`);
        } else {
          // 如果不需要进入HTML修复流程，直接更新任务状态为拆分状态
          await service.task.base.update({ status: statuses.split }, { where: { taskId } });
          logger.info(`[updateHtmlHeaderFix] 不需要进入HTML修复流程，直接进入拆分状态: ${taskId}`);
        }
      } catch (copyError) {
        logger.error(`[updateHtmlHeaderFix] 复制文件失败: ${copyError}`);

        // 发送错误通知
        const errorData = {
          type: 'error',
          receive_id: 13,
          server: { name: 'xdoc线上服务' },
          content: {
            title: 'HTML标题复制失败',
            text: `[updateHtmlHeaderFix] taskId: ${taskId}\n从 ${result} 复制到 html 失败\nerrorInfo: ${copyError}`,
          },
        };
        service.robot.sendRobotMessage(errorData);

        // 更新任务状态为错误
        await service.task.base.update({
          status: statuses.error,
          errorInfo: `HTML标题复制失败: ${copyError}`,
        }, { where: { taskId } });
      }
    } else {
      // @todo: 这个应该可以删
      logger.error('[updateHtmlHeaderFix] 修复成功但没有返回result路径');

      // 获取任务元数据
      const meta = await service.task.meta.getMetas({ taskId });
      const isAIEdit = meta.isAIEdit;
      const isAIAuto = meta.isAIAuto;

      // 判断是否需要进入HTML修复流程
      if (isAIEdit && isAIAuto) {
        // 更新任务状态为 HTML 修复处理中
        await service.task.base.update(
          { status: statuses.htmlFixProcessing },
          { where: { taskId } }
        );

        // 入队进行 HTML 修复
        const htmlUrl = service.task.base.getUrl(task.appKey, taskId, 'html');
        await service.rbs.initRBSQueue({
          task_id: task.taskId.toString(),
          task_type: 'xdoc_html_auto_fix',
          task_info: {
            task_id: task.taskId.toString(),
            html_url: htmlUrl,
            subject: meta?.subject || task.subject,
            app_key: task.appKey,
            is_ai_edit: isAIEdit,
            bucket_name: config.aliOss.bucket,
            upload_path: service.task.base.getOssKey(task.appKey, taskId, 'fixed.html'),
            get_task_parameters: [
              'task_id',
              'html_url',
              'subject',
              'is_ai_edit',
              'app_key',
              'bucket_name',
              'upload_path'
            ],
            callback_extras: [
              'task_id',
              'html_url',
              'subject',
              'app_key',
              'bucket_name',
              'upload_path'
            ],
            run_type: 'common',
            callback_url: `${config.ngrok.callbackUrl}/api/open/task/updateHtmlFix`,
            push_time: new Date().getTime(),
            timestamp: new Date().getTime(),
          },
        });

        logger.info(`[updateHtmlHeaderFix] 入队 HTML 修复成功: ${taskId}`);
      } else {
        // 如果不需要进入HTML修复流程，直接更新任务状态为拆分状态
        await service.task.base.update({ status: statuses.split }, { where: { taskId } });
        logger.info(`[updateHtmlHeaderFix] 不需要进入HTML修复流程，直接进入拆分状态: ${taskId}`);
      }
    }
    ctx.body = { status: 0 };
  }

  @validate({ taskId: 'number' })
  public async getProjectByParentTask() {
    const { service, ctx } = this;
    const { taskId } = ctx.input as {
      taskId: number;
    };
    const task = await service.task.base.getOne({ where: { parentTaskId: taskId } });
    const tasks = await service.task.base.getAll({ where: { parentTaskId: taskId } });
    if (!task) {
      return ctx.body = baseError.dataNotExistError('没找到子任务');
    }
    if (!task.bookId) {
      return ctx.body = baseError.dataNotExistError('子任务没有关联图书');
    }
    const book = await service.book.getOne({ where: { id: task.bookId } });
    if (!book) return ctx.body = baseError.dataNotExistError('没有找到子任务所在的图书');
    const project = await service.project.base.getOne({ where: { id: book.projectId } });
    if (!project) return ctx.body = baseError.dataNotExistError('没有找到子任务所在的项目');

    ctx.body = {
      status: 0,
      data: project,
      tasks,
    };
  }

  @validate({
    taskId: 'number',
    file: {
      type: 'object',
      rule: {
        pdfs: 'array?',
        fbds: 'object?',
        words: 'object?',
        zips: 'array?',
        workOrderFbdBasePath: 'string?',
        workOrderWordBasePath: 'string?',
      },
    },
  })
  public async setFiles() {
    const { service, ctx } = this;
    const { taskId, file } = ctx.input as {
      taskId: number;
      file: Partial<ITaskFiles>;
    };

    // 验证任务是否存在
    const task = await service.task.base.getOne({
      where: { taskId },
      attributes: ['taskId', 'appKey', 'bookId', 'status'],
    });

    if (!task) {
      return ctx.body = baseError.dataNotExistError('任务不存在');
    }

    this.logger.info(`[open.taskV2.setFiles]: taskId-${taskId}, file - ${JSON.stringify(file)}`);

    // 获取相关的任务ID（包括父任务和子任务）
    const taskIds = [taskId];

    // 如果当前任务是父任务，也要更新所有子任务
    const childTasks = await service.task.base.getAll({
      where: { parentTaskId: taskId },
      attributes: ['taskId'],
    });
    if (childTasks.length > 0) {
      taskIds.push(...childTasks.map((t) => t.taskId));
    }

    // 在后台处理文件更新
    ctx.runInBackground(async() => {
      try {
        // 如果有PDF文件，先处理图片转换
        // if (file.pdfs && file.pdfs.length > 0) {
        //   await service.task.taskV2.pdf2image(file.pdfs);
        // }

        // 更新任务文件信息
        await service.task.file.setFiles(taskIds, file);

        this.logger.info(`[open.taskV2.setFiles]: 成功更新任务文件 taskIds: ${taskIds.join(',')}`);
      } catch (error) {
        this.logger.error(`[open.taskV2.setFiles]: 更新任务文件失败 taskId: ${taskId}, error: ${error}`);
      }
    });

    ctx.body = { status: 0 };
  }
}
