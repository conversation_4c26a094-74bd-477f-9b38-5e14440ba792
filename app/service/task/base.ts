/**
 * @file task
 * <AUTHOR>
 */

'use strict';

import { Context } from 'egg';
import * as _ from 'lodash';
import * as urllib from 'urllib';
import ModelService from '../../core/base/modelService';
import { Attributes, defineAttributes, ETaskResourceType, ETaskType, Instance } from '../../model/task';
import { cleanJsonNodes } from '../../core/utils/htmlToJsonV4';
import * as ImageModel from '../../model/image';
import { EImageMarkStatus } from '../../model/image';
import * as SourceImageModel from '../../model/sourceImage';
import { ESourceImageStatus } from '../../model/sourceImage';
import * as sequelize from 'sequelize';
import superSequelize from '../../../typings/app/core/modelService';
import { htmlToJsonV2 } from '../../core/utils/htmlToJsonV2';
import { htmlToJson, TJsonNode } from '../../core/utils/htmlToJsonV4';
import { htmlToJsonV5 } from '../../core/utils/htmlToJsonV5';
import { getHtml, IElementNode, ITextNode, parseHtml, THtmlNode, getText } from '../../core/utils/htmlHelper';
import * as uuid from 'uuid/v4';
import { IProjectMetas } from '../../model/projectMeta';
import { findNode, iterateNode } from '../../core/utils/treeHelper';
import { formatOutputJson } from '../../core/utils/formatOutputJson';
import * as dateformat from 'dateformat';
import { Task } from './taskV2';
import { ITaskFiles } from '../../model/taskFile';
import axios from 'axios';

const katex = require('katex');
const sharp = require('sharp');

export type supportExtension =
  'pdf' // 目前大文件流程产生的pdf
  | 'html' // 输出html
  | 'diff.json.html' // 质检对比json的html
  | 'diff.docx.html' // 质检对比docx的html
  | 'internal.json' // 中间 json
  | 'json' // 输出 json
  | 'diff.json' // 质检对比的 json
  | 'mark.json' // 中间标注 json
  | 'mark.official.json' // 手工标注过的输出json
  | 'meta.json' // 额外字段信息
  | 'origin.docx' // docx
  | 'docx' // docx
  | 'machine.docx' // 机器直接生成docx
  | 'origin.html' // 机器识别html
  | 'machine.html' // 机器识别后公式清洗的html
  | 'mark.html' // 标注html
  | 'json.docx' // 七天 json 转 docx
  | 'flow.json.docx' // 七天 json 转 docx 平铺
  | 'json.docx.zip' // 七天 每题一个docx
  | 'formatted.html' // 数据清洗过的Html
  | 'diff.json.formatted.html' // 质检对比清洗过的json和html
  | 'diff.docx.formatted.html' // 质检对比清洗过的docx和html
  | 'formatted.internal.json' // 数据清洗过的中间json
  | 'formatted.json' // 数据清洗过的输出json
  | 'preprocessed.internal.json' // json预处理过的中间json
  | 'preprocessed.json' // json预处理过的输出json
  | 'diff.formatted.json' // 质检对比数据清洗过的输出json
  | 'formatted.docx' // 数据清洗过的docx
  | 'clean.formatted.html' // 清除掉编辑器标签的清洗过的html
  | 'pdf' // 对照PDF
  | 'sample.html' // 样例 html
  // AI EDIT
  | 'ai.flatten.json' // ai 打平后的 json(每个题一个文件)
  | 'fixed.json' // 修复后的 html 转出的 JSON
  | 'fixed.html'
  | 'backup.html' // ai 清洗备份html（ai修复前）
  | 'done.docx' // ai 校对后的 word
  | 'wps.json' // wps 实时编辑 json
  | 'wps.html' // wps 实时编辑算法返回的结果转的 html
  | 'wps.docx' // wps 实时编辑算法返回的结果生成的 docx
  | 'ai.process.json' // ai 处理后的 json
  | 'header_fixed.html' // wps 实时编辑 json
  | 'header_fixed.html' // wps 实时编辑 json
  ;

export type TaskProcessTypes = 'imageStructProcessor' | 'imageColumnProcessor';

export default class TaskService extends ModelService<Instance, Attributes> {
  constructor(ctx: Context) {
    super(ctx, ctx.app.model.Task, defineAttributes);
  }

  private getTaskConfigKey(taskId: number, type: TaskProcessTypes) {
    return `task:${taskId}:${type}:config`;
  }

  public statuses = {
    contentError: -2, // 内容检查异常
    error: -1,
    init: 0,
    pending: -201, // word/fbd/pdf 预处理中
    updating: -202, // word/fbd 回调更新中
    split: -203, // word/fbd/pdf 等待文件分割
    spliting: -204, // word/fbd 文件分割中
    sysSpliting: -205, // word/fbd/pdf 系统进行文件分割中
    splited: -206, // word/fbd/pdf 文件分割完成
    splitFailed: -207, // word/fbd 文件分割失败
    preCropCheck: -105, // 预切图检查（等待系统检查是否需要手工切图）
    preCropChecked: -104, // 预切图检查完成（系统检查需要切图，此时管理员可以处理）
    preCropProcessed: -102, // 预切图处理完成（管理员提交，等待系统将原图拆分成图）
    columnQueue: -101, // 等待进入划块队列
    columnAutoProcessing: -100, // 机器划块识别中
    columnAutoProcessed: -99, // 机器划块识别完毕
    columnProcessing: -98, // 人工划块处理中
    columnProcessed: 1, // 人工划块处理完毕
    autoProcessing: 2, // 机器内容识别中
    unmarked: 3, // 机器识别完成，等待标注
    marking: 4, // 标注中
    unreviewed: 5, // 标注完成，等待审核
    reviewing: 6, // 审核中
    dataCleaning: 61, // 数据清洗中
    dataCleanfailed: 62, // 数据清洗 / json预处理 失败
    jsonPreProcessing: 63, // json预处理中
    reviewed: 7, // 审核完成
    operatAdmin: 8, // 管理员修改
    closed: 10, // 任务取消
    statError: 9, // 统计失败
    ocrProcessing: 11, // 新流程 ocr 识别中
    htmlFixProcessing: 12, // html 内容修复中
    htmlHeaderFixProcessing: 13, // html 标题修复中
  };

  public openStatuses = {
    init: 1,
    processing: 2,
    successful: 3,
    failed: 4,
    revoked: 5,
  };

  public async getId(): Promise<number> {
    const {
      ctx,
      config,
    } = this;
    const {
      appKey,
      appSecret,
    } = config.idOnly.taskId;
    const res = await ctx.curl(`${config.idOnly.api}/id/getOne`, {
      data: {
        appKey,
        appSecret,
      },
      dataType: 'json',
    });
    if (!res.data || !res.data.data) {
      return 0;
    }
    return res.data.data.id;
  }

  public async getBatchIds(len: number): Promise<number[]> {
    const {
      ctx,
      config,
    } = this;
    const {
      appKey,
      appSecret,
    } = config.idOnly.taskId;
    const res = await ctx.curl(`${config.idOnly.api}/id/getBatch`, {
      data: {
        appKey,
        appSecret,
        size: len,
      },
      dataType: 'json',
    });
    if (!res.data || !res.data.data) {
      return [];
    }
    return res.data.data.ids;
  }

  public getUrl(appKey: string, taskId: number | string, extension: supportExtension, timestamp = true, internal = false) {
    const { config } = this;
    const host = internal ? config.aliOss.privateHost : config.aliOss.host;
    return `${host}${this.getOssKey(appKey, taskId, extension)}${timestamp ? `?time=${Number(new Date())}` : ''}`;
  }

  public getSampleUrl(appKey: string, key: number | string, extension: supportExtension, timestamp = true, internal = false) {
    const { config } = this;
    const host = internal ? config.aliOss.privateHost : config.aliOss.host;
    return `${host}${this.getSampleOssKey(appKey, key, extension)}${timestamp ? `?time=${Number(new Date())}` : ''}`;
  }

  public getAiUrl(appKey: string, taskId: number | string, extension: supportExtension, timestamp = true, internal = false) {
    const { config } = this;
    const host = internal ? config.aliOss.privateHost : config.aliOss.host;
    return `${host}${this.getAiOssKey(appKey, taskId, extension)}${timestamp ? `?time=${Number(new Date())}` : ''}`;
  }

  public getAiOssKey(appKey: string, taskId: number | string, extension: supportExtension) {
    return `open/${appKey}/ai/${taskId}.${extension}`;
  }

  public getOssKey(appKey: string, taskId: number | string, extension: supportExtension) {
    return `open/${appKey}/task/${taskId}.${extension}`;
  }

  public getSampleOssKey(appKey: string, key: number | string, extension: supportExtension) {
    return `open/${appKey}/sample/${key}.${extension}`;
  }

  public getOssBaseUrl(appKey: string, taskId: number, internal = false) {
    const { config } = this;
    const host = internal ? config.aliOss.privateHost : config.aliOss.host;
    return `${host}open/${appKey}/task/${taskId}`;
  }

  public async getOssData(appKey: string, taskId: number | string, extension: supportExtension, timestamp = true, internal = true) {
    const url = this.getUrl(appKey, taskId, extension, timestamp, internal);
    return this.service.oss.fetch(url, extension);
  }

  /**
   * 获取oss上样例文件
   * @param appKey
   * @param taskId
   * @param extension
   * @param timestamp
   * @param internal
   */
  public async getSampleOssData(appKey: string, taskId: number | string, extension: supportExtension, timestamp = true, internal = true) {
    const url = this.getSampleUrl(appKey, taskId, extension, timestamp, internal);
    return this.service.oss.fetch(url, extension);
  }

  public async hasOssData(appKey: string, taskId: number | string, extension: supportExtension, options = {}) {
    const ossClient = this.service.oss.createOss();
    const key = `open/${appKey}/task/${taskId}.${extension}`;
    try {
      await ossClient.head(key, options);
      return true;
    } catch (error) {
      if ((error as any).code === 'NoSuchKey') {
        return false;
      }
    }
  }

  public async setOssData(appKey: string, taskId: number | string, extension: supportExtension, data: any) {
    const key = this.getOssKey(appKey, taskId, extension);
    const url = await this.service.oss.upload(key, data, 'string');
    return url;
  }

  /**
   * 上传样例文件到oss
   * @param appKey
   * @param taskId
   * @param extension
   * @param data
   */
  public async setSampleOssData(appKey: string, taskId: number | string, extension: supportExtension, data: any) {
    const key = this.getSampleOssKey(appKey, taskId, extension);
    const url = await this.service.oss.upload(key, data, 'string');
    return url;
  }

  public async copyOssData(appKey: string, taskId: number | string, source: supportExtension, target: supportExtension) {
    const ossClient = this.service.oss.createOss();
    const sourceKey = `open/${appKey}/task/${taskId}.${source}`;
    const key = `open/${appKey}/task/${taskId}.${target}`;
    await ossClient.copy(key, sourceKey);
  }

  public async setOssMeta(appKey: string, taskId: number, meta: Partial<IProjectMetas>) {
    const { service } = this;
    const data = await service.project.meta.getMetaInfo(meta);
    const url = await this.setOssData(appKey, taskId, 'meta.json', {
      ...data,
      ..._.pick(meta, 'bookName'),
    });
    return url;
  }

  public async changeChapter({
    allImageIds,
    appKey,
    index,
    body,
    level,
  }: { allImageIds: string[], appKey: string, index: number, body?: string, level?: number }) {
    const { service } = this;
    const nodes = await Promise.all(allImageIds.map(async(imageId) => {
      const html = await service.image.getOssData(appKey, imageId, 'html');
      return parseHtml(html);
    }));
    let initial = 0;
    let i = 0;
    let changeChapter: any = null;
    // 按索引查找对应标题；
    const targetNode = nodes.find((item, itemIndex) => {
      for (const { node } of iterateNode(item)) {
        if (node.type === 'element' && node.dataset.label === 'header') {
          initial += 1;
          if (index === initial - 1) {
            i = itemIndex;
            changeChapter = node;
            return true;
          }
        }
      }
    });
    if (typeof level === 'number' && changeChapter) {
      changeChapter.dataset.level = level.toString();
      changeChapter.attrs['data-level'] = level.toString();
    }
    if (typeof body === 'string' && changeChapter) {
      const parseBody: any = parseHtml(body)[0] ? parseHtml(body)[0] : {
        cls: {},
        style: {},
        children: [],
      };
      changeChapter.children = parseBody.children;
      changeChapter.cls = parseBody.cls;
      if (parseBody.attrs.class) {
        changeChapter.attrs.class = parseBody.attrs.class;
      } else {
        delete changeChapter.attrs.class;
      }
      changeChapter.style = parseBody.style;
    }
    return {
      html: getHtml(targetNode || []),
      imageId: allImageIds[i],
    };
  }

  public async checkOssData({
    appKey,
    taskId,
    srcExtension,
    dstExtension,
  }: { appKey: string; taskId: number; srcExtension: supportExtension; dstExtension: supportExtension; }) {
    const { ctx } = this;
    const src_file = this.getUrl(appKey, taskId, srcExtension);
    const dst_file = this.getUrl(appKey, taskId, dstExtension);
    let errorInfo = '';
    try {
      const res = await ctx.curl('http://47.100.171.12:55556/consist_check', {
        method: 'POST',
        dataType: 'json',
        data: {
          src_file,
          dst_file,
          task_id: taskId,
        },
        timeout: 60 * 1000,
      });
      const {
        pass_check,
        diff_char,
      } = res.data;
      if (!pass_check) {
        errorInfo = `内容一致性检测异常【${srcExtension}2${dstExtension}】：${JSON.stringify(diff_char)}`;
      }
    } catch (e) {
      errorInfo = `内容一致性检测异常【${srcExtension}2${dstExtension}】：${JSON.stringify(e)}`;
    }
    return errorInfo;
  }

  public async refreshChapter({
    allImageIds,
    appKey,
  }: { allImageIds: string[], appKey: string }) {
    const {
      service,
      ctx,
    } = this;
    const nodes = await Promise.all(allImageIds.map(async(imageId) => {
      const html = await service.image.getOssData(appKey, imageId, 'html');
      return parseHtml(html);
    }));
    const chapterNodeArr: any[] = [];
    // 按索引查找对应标题；
    nodes.forEach((item) => {
      for (const { node } of iterateNode(item)) {
        if (node.type === 'element' && node.dataset.label === 'header') {
          chapterNodeArr.push(node);
        }
      }
    });
    const chapterHtmlArr = chapterNodeArr.map((node) => getText([node]));
    let chapterLevelArr: number[] = [];
    try {
      const res = await ctx.curl('http://192.168.2.91:5314/header_classify', {
        method: 'POST',
        dataType: 'json',
        data: { text_list: JSON.stringify(chapterHtmlArr) },
        timeout: 60 * 1000,
      });
      const preds = res.data.preds;
      if (Array.isArray(preds)) {
        const levelArr = preds.map((item) => Number(item.replace('header', '')));
        if (levelArr.every((item) => !isNaN(item))) {
          chapterLevelArr = levelArr;
        }
      }
      this.logger.info(`调用机器调整输出层级, 输入：${JSON.stringify(chapterHtmlArr)}, 返回：${JSON.stringify(res)}`);
    } catch (e) {
      this.logger.error(`调用机器调整标题接口出错：${e}`);
    }
    if (chapterHtmlArr.length !== chapterLevelArr.length) return '';
    chapterNodeArr.forEach((node, i) => {
      const level = chapterLevelArr[i];
      if (typeof level === 'number') {
        node.dataset.level = level.toString();
        node.attrs['data-level'] = level.toString();
      }
    });

    return nodes.map((node) => getHtml(node));
  }

  public async changeHtmlChapter(html: string, index: number, body: string | undefined, level: number | undefined) {
    const nodes = parseHtml(html);
    let initial = 0;
    let changeChapter: any = null;
    for (const { node } of iterateNode(nodes)) {
      if (node.type === 'element' && node.dataset.label === 'header') {
        initial += 1;
        if (index === initial - 1) {
          changeChapter = node;
          break;
        }
      }
    }
    if (typeof level === 'number' && changeChapter) {
      changeChapter.dataset.level = level.toString();
      changeChapter.attrs['data-level'] = level.toString();
    }
    if (typeof body === 'string' && changeChapter) {
      const parseBody: any = parseHtml(body)[0] ? parseHtml(body)[0] : {
        cls: {},
        style: {},
        children: [],
      };
      changeChapter.children = parseBody.children;
      changeChapter.cls = parseBody.cls;
      if (parseBody.attrs.class) {
        changeChapter.attrs.class = parseBody.attrs.class;
      } else {
        delete changeChapter.attrs.class;
      }
      changeChapter.style = parseBody.style;
    }
    return getHtml(nodes);
  }

  public async combineAndUploadByImageIds(appKey: string, taskId: number, imageIds: string[]) {
    const html = await this.combineByImageIds(appKey, imageIds, taskId);
    await this.setOssData(appKey, taskId, 'html', html);
    return html;
  }

  public async combineByImageIds(appKey: string, imageIds: string[], taskId: number) {
    const { service } = this;
    const trees = await Promise.all(imageIds.map(async(imageId) => {
      const _html = await service.image.getOssData(appKey, imageId, 'html');
      const html = await this.service.task.stat.processLatex(_html, taskId);
      const nodes = parseHtml(html);
      if (service.appl.isNewVersion(appKey)) {
        // 合并的时候，对每个图片的最外层标签添加 data-img-id，指向当前图片
        nodes.forEach((node) => {
          if (node.type !== 'element') {
            return;
          }
          node.attrs['data-img-id'] = node.attrs['data-img-id'] || imageId;
        });
      }
      const hasBase64Img = await this.service.oss.uploadBase64forHtml(`open/${appKey}/image/tmp/`, nodes);
      if (hasBase64Img) {
        await this.service.image.setOssData(appKey, imageId, 'html', getHtml(nodes));
      }
      if (service.appl.isNewVersion(appKey)) {
        if (this.isEmptyNode(nodes[0])) { // 去除开头的空白
          nodes.splice(0, 1);
        }
        if (this.isEmptyNode(nodes[nodes.length - 1])) { // 去除结尾的空白
          nodes.splice(nodes.length - 1, 1);
        }
      }
      return nodes;
    }));
    if (!trees.length) return '';
    let nodes: IElementNode[] = [];
    // 拼接相邻图片的 p，两个 p 变成一个 p
    trees.forEach((tree) => {
      const currentNodes = tree.filter((node) => node.type === 'element') as IElementNode[];
      const preTail = nodes[nodes.length - 1];
      const currentHead = currentNodes[0];
      if (this.canCombine(preTail, currentHead)) {
        const preLeaf = findNode(
          preTail.children,
          ({ node }) => node.type === 'text' && Boolean(node.content),
          { back: true }
        ) as ITextNode;
        const currentLeaf = findNode(
          preTail.children,
          ({ node }) => node.type === 'text' && Boolean(node.content)
        ) as ITextNode;
        if (
          preLeaf && currentLeaf &&
          /[\x20-\x7f]$/.test(preLeaf.content) &&
          /^[\x20-\x7f]/.test(currentLeaf.content)
        ) { // 前后内容为英文
          // 拼接时中间插入一个空格
          preTail.children = [...preTail.children, {
            type: 'text',
            content: '&nbsp;',
          }, ...currentHead.children];
        } else {
          preTail.children = [...preTail.children, ...currentHead.children];
        }
        nodes = nodes.concat(currentNodes.slice(1));
      } else {
        nodes = nodes.concat(currentNodes);
      }
    });
    const html = getHtml(nodes);
    return html;
  }

  private isEmptyNode(node?: THtmlNode) {
    // 是否为空白段
    if (!node) return false;
    if (node.type === 'comment') return true;
    if (node.type === 'text') {
      return /^(\s|&nbsp;)*$/.test(node.content);
    }
    if (node.type !== 'element') return false;
    if (!node.children.length) {
      return node.tagName !== 'hr';
    }
    if (node.children.length > 1) return false;
    const sub = node.children[0];
    if (sub.type !== 'text') return false;
    return /^(\s|&nbsp;)*$/.test(sub.content);
  }

  private canCombine(tail: IElementNode, head: IElementNode) {
    // 相邻图片的段文本是否可合并
    if (!tail || !head) {
      return false;
    }
    // 仅合并p标签
    if (tail.tagName !== 'p' || head.tagName !== 'p') {
      return false;
    }
    // 目录。不合并
    if (tail.dataset.label === 'header' || head.dataset.label === 'header') {
      return false;
    }
    // 相邻data-label不同。不合并
    if (tail.dataset.label !== head.dataset.label) {
      return false;
    }
    // 新段落，包含：对齐、缩进。不合并
    if (head.attrs.class &&
      (head.attrs.class.includes('indent') || /align-(center|right|justify)/.test(head.attrs.class))) {
      return false;
    }
    // 前一个段落，包含：对齐。不合并
    if (tail.attrs.class && /align-(center|right|justify)/.test(tail.attrs.class)) {
      return false;
    }
    // 新段落包含：题号、选项号。不合并
    if (findNode(head.children, ({ node }) => {
      return node.type === 'element' && ['quest_num', 'choice_option'].includes(node.dataset.label!);
    })) {
      return false;
    }
    // 前一个段落，以括号结尾。不合并
    const tailEnd = this.getSideElementNode(tail.children, 'end');
    if (tailEnd && tailEnd.type === 'element' && tailEnd.dataset.label === 'bracket') {
      return false;
    }
    // 只有图片。不合并
    if (tail.children.length === 1 && tail.children[0].tagName === 'img' ||
      head.children.length === 1 && head.children[0].tagName === 'img') {
      return false;
    }
    return _.isEqual(_.omit(tail.dataset, 'img-id'), _.omit(head.dataset, 'img-id'));
  }

  private getSideElementNode(nodes: THtmlNode[], side: 'start' | 'end') {
    let i = side === 'start' ? 0 : nodes.length - 1;
    while (nodes[i]) {
      const node = nodes[i];
      if (node.type !== 'text' || node.content.trim()) return node;
      i += side === 'start' ? 1 : -1;
    }
  }

  public async convert2Json(appKey: string, taskId: number, html?: string) {
    let str = html;
    if (!html) {
      str = await this.getOssData(appKey, taskId, 'html');
    }
    if (!str) {
      return [];
    }
    let json: TJsonNode[];
    if (this.service.appl.isNewVersion(appKey)) {
      json = htmlToJson({
        html: str,
        isOfficial: false,
      });
    } else {
      json = htmlToJsonV2({
        html: str,
        isOfficial: false,
      });
    }
    formatOutputJson(json);
    return json;
  }

  public async convert2JsonV2(html: string, subject: string) {
    const str = html;
    if (!str) {
      return [];
    }
    const json = htmlToJsonV5({
      html: str,
      isOfficial: false,
    }, subject);
    formatOutputJson(json);
    return json;
  }

  public async checkHtml(html: string) {
    const json = htmlToJson({
      html,
      isOfficial: false,
    });
    let curLatex = '';
    try {
      for (const { node } of iterateNode(json)) {
        const content = node?.content;
        if (content) {
          Object.keys(content).forEach((k) => {
            const v = content[k];
            if (typeof v === 'string') {
              const latexs = v.match(/\$\$([\s\S]*?)\$\$/g);
              if (latexs) {
                latexs.map((item) => item.replace(/\$\$([\s\S]*?)\$\$/g, (_match, latex) => `${latex}`))
                  .forEach((item) => {
                    curLatex = item.replace(/\\text/g, '');
                    katex.renderToString(curLatex);
                  });
              }
            }
          });
        }
      }
    } catch (e) {
      return `检测到HTML中的公式出现异常: ${curLatex} ------ ${e}`;
    }
  }

  public getCatalog(taskKey: string, taskId: number, json: TJsonNode[]) {
    const catalog: { level: string; name: string; taskKey: string; taskId: number; imageId: string }[] = [];
    for (const item of iterateNode(json)) {
      if (item.node.node_type === 'chapter') {
        catalog.push({
          level: `h${item.node.content.level}`,
          name: item.node.node_name,
          taskKey,
          taskId,
          imageId: item.node.imgId!,
        });
      }
    }
    return catalog;
  }

  public async relatedCreate(
    task: {
      taskId?: number;
      appKey: string;
      subject: 'math' | 'chinese' | 'en-math';
      callbackUrl: string;
      extra: string;
      priority?: number;
      taskName: string;
      images: string[] | { id?: string; uri?: string; name?: string; filename?: string }[];
      open: boolean;
      isTest: boolean;
      bookId?: number;
      bookOrder?: number;
      taskType?: ETaskType;
      timeLimit?: number;
      timeWarning?: number;
      ticketId?: string;
    }, projectMeta: Partial<IProjectMetas>
  ) {
    const {
      service,
      statuses,
      ctx,
      logger,
    } = this;
    const taskId: number = await service.task.base.getId();
    if (!taskId) {
      throw new Error('系统异常，任务创建失败');
    }
    logger.info(`[创建任务] Task ${taskId} relatedCreate start.`);
    const {
      appKey,
      priority,
      subject,
      callbackUrl,
      extra,
      taskName,
      images,
      isTest,
      taskType = ETaskType.unset,
      timeLimit = 24 * 60,
      timeWarning = 0.6,
      ticketId,
      open,
    } = task;
    await service.task.base.create({
      taskId,
      appKey,
      priority,
      subject,
      callbackUrl,
      isTest,
      extra,
      taskName,
      isCallback: false,
      callbackError: false,
      status: statuses.init,
      errorInfo: '',
      markUserId: 0,
      reviewUserId: 0,
      splitUserId: 0,
      operatAdminUserId: 0,
      preprocessUserId: 0,
      imageCount: images.length,
      catalog: '',
      bookId: task.bookId || 0,
      bookOrder: task.bookOrder || 0,
      taskType,
      timeLimit,
      timeWarning,
      countdown: timeLimit,
      ticketId,
    });
    logger.info(`[创建任务] Task ${taskId}-ticketId:${ticketId || ''}create done.`);
    // 异步创建图片任务
    ctx.runInBackground(async() => {
      logger.info(`[创建任务] Task ${taskId}-ticketId:${ticketId || ''} execute source images in background start.`);
      // 把参数存到 redis
      // 目前只给有 tickedId 的任务存
      if (task.ticketId) {
        try {
          task.taskId = taskId;
          await this.saveTaskParams(taskId, JSON.stringify(task));
          this.logger.info(`[创建任务] Task ${taskId}-ticketId:${ticketId || ''} save task params done.`);
        } catch (e) {
          logger.info(e);
        }
      }
      try {
        const sourceImages: SourceImageModel.Attributes[] = [];
        let errorInfo = '';
        for (let i = 0; i < images.length; i += 1) {
          const item = images[i];
          /*
           * Tips：
           * 这里拿到的图片 uri 可能是前端计算出来的 md5 值，
           * 通过工单系统传递过来的。
           */
          const uri = typeof item === 'string' ? item : item.uri || item.id;
          const filename = typeof item === 'string' ? `${i * 5}.jpg` : item.name || item.filename || `${i * 5}.jpg`;
          const imageId = open ? uuid().replace(/-/g, '') : uri;
          logger.info(`[创建任务] Task ${taskId}-ticketId:${ticketId || ''} create source image ${imageId} start.`);
          // 过滤掉重复出现的图片
          if (sourceImages.some((s) => s.imageId === imageId)) {
            logger.info(`[创建任务] Task ${taskId}-ticketId:${ticketId || ''} create source image ${imageId} exist.`);
            continue;
          }
          if (open) {
            const error = await this.uploadImage(appKey, uri, imageId, `${taskId}`, ticketId);
            if (error) {
              errorInfo = error;
              break;
            }
          }
          sourceImages.push({
            taskId,
            imageId,
            appKey,
            filename,
            bookId: task.bookId || 0,
            taskOrder: i + 1,
            status: ESourceImageStatus.init,
            result: '',
            info: '',
          });
        }
        if (errorInfo || !sourceImages.length && open) {
          errorInfo = errorInfo || '没有图片';
          if (!ticketId) {
            await this.update({
              errorInfo,
              status: statuses.error,
            }, { where: { taskId } });
          } else {
            // await service.workSheet.submitError({
            //   ticketId,
            //   errorInfo,
            // });
            // try {
            //   // 这里如果失败应该入队重试，而不是直接改状态删除
            //   await this.deleteTaskParams(taskId);
            // } catch (e) {
            //   logger.info(`[创建任务] Task ${taskId}-ticketId:${ticketId || ''} delete task params error.`);
            // }
          }
          if (callbackUrl) {
            await service.task.callback.callback({
              taskId,
              appKey,
              callbackUrl,
              errorInfo,
              extra: extra || '',
              taskName: taskName || '',
              status: statuses.error,
            });
          }
          return;
        }
        await service.sourceImage.bulkCreate(sourceImages);
        logger.info(`[创建任务] Task ${taskId}-ticketId:${ticketId || ''} bulk create source images done.`);
        if (ticketId) {
          try {
            await this.deleteTaskParams(taskId);
            logger.info(`[创建任务] Task ${taskId}-ticketId:${ticketId || ''} delete task params done.`);
          } catch (e) {
            logger.info(`[创建任务] Task ${taskId}-ticketId:${ticketId || ''} delete task params error.`);
          }
        }
        await super.update({ status: statuses.preCropCheck }, { where: { taskId } });
        logger.info(`[创建任务] Task ${taskId}-ticketId:${ticketId || ''} update status to statuses.preCropCheck done.`);
        task.taskId = taskId;
        await this.pushPreCropCheck([task], { type: ticketId ? 'v2' : (projectMeta.imageHtmlVersion || '') });
        // 更新 meta
        let meta: any;
        if (ticketId) {
          meta = {imageHtmlVersion: 'v2'};
        } else {
          meta = projectMeta;
        }
        await service.task.meta.setMetas([taskId], meta);
        logger.info(`[创建任务] Task ${taskId} meta: ${JSON.stringify(projectMeta)}`);
        // 创建成功，状态流转，删除 redis 中的请求参数
        logger.info(`[创建任务] Task ${taskId}-ticketId:${ticketId || ''} push pre-crop queue done.`);
      } catch (e) {
        logger.error(e);
        if (!ticketId) {
          await this.update({
            errorInfo: '创建任务异常: ' + (e as any).message,
            status: statuses.error,
          }, { where: { taskId } });
        }
        if (ticketId) {
          // 状态正常流转，可以从 redis 删掉请求参数了
          try {
            // await this.deleteTaskParams(taskId);
          } catch (_e) {
            logger.info(`[创建任务] Task ${taskId}-ticketId:${ticketId || ''} delete task params error.`);
          }
          // await service.workSheet.submitError({
          //   ticketId: ticketId,
          //   errorInfo: '创建任务异常' + (e as any).message,
          // });
        }
        await service.task.callback.callback({
          taskId,
          appKey,
          callbackUrl,
          errorInfo: '系统异常',
          extra: extra || '',
          taskName: taskName || '',
          status: statuses.error,
        });
      }
    });
    return taskId;
  }

  /**
   * 目前仅限于学科网转存（sharp）失败或者转存卡住的图文任务
   * @param task
   */
  public async relatedCreateWidthTaskId(
    task: {
      taskId: number;
      appKey: string;
      subject: 'math' | 'chinese' | 'en-math';
      callbackUrl: string;
      extra: string;
      priority?: number;
      taskName: string;
      images: string[] | { id?: string; uri?: string; name?: string; filename?: string }[];
      open: boolean;
      isTest: boolean;
      bookId?: number;
      bookOrder?: number;
      taskType?: ETaskType;
      timeLimit?: number;
      timeWarning?: number;
      ticketId?: string;
    }
  ) {
    const {
      service,
      statuses,
      ctx,
      logger,
    } = this;

    const {
      taskId,
      appKey,
      callbackUrl,
      extra,
      taskName,
      images,
      ticketId,
      open,
    } = task;
    logger.info(`[重新创建任务] Task ${taskId}-ticketId:${ticketId || ''}create done.`);
    // - 正常来说，图片转录进来的数据，任务状态一定是初始化，如果不是初始化，可能说明已经进入下一步了，直接返回即可。
    // - 也不存在任务状态是失败的可能，这里提前做过处理。
    const _task = await this.getOne({ where: { taskId } });
    if (
      (_task && _task.status !== statuses.init) ||
      !_task
    ) {
      return taskId;
    }
    // 更新任务状态为 初始化 状态
    // await this.update({ status: statuses.init }, { where: { taskId } });
    // 异步创建图片任务
    ctx.runInBackground(async() => {
      logger.info(`[重新创建任务] Task ${taskId}-ticketId:${ticketId || ''} execute source images in background start.`);
      try {
        const sourceImages: SourceImageModel.Attributes[] = [];
        let errorInfo = '';
        for (let i = 0; i < images.length; i += 1) {
          const item = images[i];
          /*
           * Tips：
           * 这里拿到的图片 uri 可能是前端计算出来的 md5 值，
           * 通过工单系统传递过来的。
           */
          const uri = typeof item === 'string' ? item : item.uri || item.id;
          const filename = typeof item === 'string' ? `${i * 5}.jpg` : item.name || item.filename || `${i * 5}.jpg`;
          const imageId = open ? uuid().replace(/-/g, '') : uri;
          logger.info(`[重新创建任务] Task ${taskId}-ticketId:${ticketId || ''} create source image ${imageId} start.`);
          // 过滤掉重复出现的图片
          if (sourceImages.some((s) => s.imageId === imageId)) {
            logger.info(`[重新创建任务] Task ${taskId}-ticketId:${ticketId || ''} create source image ${imageId} exist.`);
            continue;
          }
          if (open) {
            const error = await this.uploadImage(appKey, uri, imageId, `${taskId}`, ticketId);
            if (error) {
              errorInfo = error;
              break;
            }
          }
          sourceImages.push({
            taskId,
            imageId,
            appKey,
            filename,
            bookId: task.bookId || 0,
            taskOrder: i + 1,
            status: ESourceImageStatus.init,
            result: '',
            info: '',
          });
        }
        if (errorInfo || !sourceImages.length && open) {
          errorInfo = errorInfo || '没有图片';
          await this.update({
            errorInfo,
            status: statuses.error,
          }, { where: { taskId } });
          if (ticketId) {
            await service.workSheet.submitError({
              ticketId,
              errorInfo,
            });
          }
          if (callbackUrl) {
            await service.task.callback.callback({
              taskId,
              appKey,
              callbackUrl,
              errorInfo,
              extra: extra || '',
              taskName: taskName || '',
              status: statuses.error,
            });
          }
          return;
        }
        await service.sourceImage.bulkCreate(sourceImages, { updateOnDuplicate: ['taskId'] });
        logger.info(`[重新创建任务] Task ${taskId}-ticketId:${ticketId || ''} bulk create source images done.`);
        await super.update({ status: statuses.preCropCheck }, { where: { taskId } });
        logger.info(`[重新创建任务] Task ${taskId}-ticketId:${ticketId || ''} update status to statuses.preCropCheck done.`);
        await this.pushPreCropCheck([task], { type: '' });

        logger.info(`[重新创建任务] Task ${taskId}-ticketId:${ticketId || ''} push pre-crop queue done.`);
      } catch (e) {
        logger.error(e);
        await this.update({
          errorInfo: '重新创建任务异常: ' + (e as any).message,
          status: statuses.error,
        }, { where: { taskId } });
        if (ticketId) {
          await service.workSheet.submitError({
            ticketId,
            errorInfo: '重新创建任务' + (e as any).message,
          });
        }
        await service.task.callback.callback({
          taskId,
          appKey,
          callbackUrl,
          errorInfo: '系统异常',
          extra: extra || '',
          taskName: taskName || '',
          status: statuses.error,
        });
      }
    });
    return taskId;
  }

  public async wordCreate(
    task: {
      appKey: string;
      subject: 'math' | 'chinese' | 'en-math';
      callbackUrl: string;
      extra: string;
      priority?: number;
      taskName: string;
      inputType: 'id' | 'uri';
      words: string[];
      open: boolean;
      isTest: boolean;
      bookId?: number;
      bookOrder?: number;
      taskType?: ETaskType;
      timeLimit?: number;
      timeWarning?: number;
    }
  ) {
    const {
      service,
      statuses,
      ctx,
      logger,
    } = this;
    const taskIds: number[] = await service.task.base.getBatchIds(task.words.length);
    if (!taskIds || taskIds.length !== task.words.length) {
      throw new Error('系统异常，任务创建失败');
    }
    const {
      appKey,
      subject,
      callbackUrl,
      extra,
      taskName,
      priority,
      inputType,
      words,
      isTest,
      taskType = ETaskType.unset,
      timeLimit = 24 * 60,
      timeWarning = 0.6,
    } = task;
    const tasks = taskIds.map((taskId, i) => {
      return {
        taskId,
        appKey,
        subject,
        callbackUrl,
        priority,
        isTest,
        extra,
        taskName: taskIds.length > 1 ? `${taskName}-${i + 1}` : taskName,
        isCallback: false,
        callbackError: false,
        status: statuses.init,
        errorInfo: '',
        markUserId: 0,
        reviewUserId: 0,
        splitUserId: 0,
        preprocessUserId: 0,
        operatAdminUserId: 0,
        imageCount: 0,
        catalog: '',
        bookId: task.bookId || 0,
        bookOrder: task.bookOrder == null ? 0 : task.bookOrder + i,
        taskType,
        timeLimit,
        timeWarning,
        countdown: timeLimit,
        resourceType: ETaskResourceType.WORD,
      };
    });
    await service.task.base.bulkCreate(tasks);
    const handleOne = async(word: string, taskId: number) => {
      let errorInfo: string | undefined;
      try {
        if (inputType === 'uri') {
          errorInfo = await this.uploadWord(appKey, word, taskId);
        } else {
          const ossClient = this.service.oss.createOss();
          await ossClient.copy(`open/${appKey}/task/${taskId}.origin.docx`, `open/${appKey}/word/${word}.docx`);
        }
        if (!errorInfo) {
          errorInfo = await this.initWordQueue(appKey, taskId, subject);
        }
        if (!errorInfo) {
          await super.update({ status: statuses.pending }, { where: { taskId } });
        }
      } catch (e) {
        logger.error(e);
        errorInfo = (e as any).message || '系统异常';
      }
      if (errorInfo) {
        await this.update({
          errorInfo,
          status: statuses.error,
        }, { where: { taskId } });
        await service.task.callback.callback({
          taskId,
          appKey,
          callbackUrl,
          errorInfo,
          extra: extra || '',
          taskName: taskName || '',
          status: statuses.error,
        });
      }
    };
    // 异步创建图片任务
    ctx.runInBackground(async() => {
      for (let i = 0, len = words.length; i < len; i += 1) {
        const word = words[i];
        const taskId = taskIds[i];
        try {
          await handleOne(word, taskId);
        } catch (e) {
          logger.error(`handle task error ${e}`);
        }
      }
    });
    return taskIds;
  }

  public async fbdCreate(
    task: {
      appKey: string;
      subject: 'math' | 'chinese' | 'en-math';
      callbackUrl: string;
      extra: string;
      priority?: number;
      taskNames: string[];
      open: boolean;
      isTest: boolean;
      bookId?: number;
      bookOrder?: number;
      taskType?: ETaskType;
      timeLimit?: number;
      timeWarning?: number;
    }
  ) {
    const {
      service,
      statuses,
    } = this;
    const taskIds: number[] = await service.task.base.getBatchIds(task.taskNames.length);
    if (!taskIds || taskIds.length !== task.taskNames.length) {
      throw new Error('系统异常，任务创建失败');
    }
    const {
      appKey,
      subject,
      callbackUrl,
      extra,
      taskNames,
      priority,
      isTest,
      taskType = ETaskType.unset,
      timeLimit = 24 * 60,
      timeWarning = 0.6,
    } = task;
    const tasks = taskIds.map((taskId, i) => {
      return {
        taskId,
        appKey,
        subject,
        callbackUrl,
        isTest,
        priority,
        extra,
        taskName: taskNames[i],
        isCallback: false,
        callbackError: false,
        status: statuses.init,
        errorInfo: '',
        markUserId: 0,
        reviewUserId: 0,
        splitUserId: 0,
        preprocessUserId: 0,
        operatAdminUserId: 0,
        imageCount: 0,
        catalog: '',
        bookId: task.bookId || 0,
        bookOrder: task.bookOrder == null ? 0 : task.bookOrder + i,
        taskType,
        timeLimit,
        timeWarning,
        countdown: timeLimit,
        resourceType: ETaskResourceType.FBD,
      };
    });
    await service.task.base.bulkCreate(tasks);
    return taskIds;
  }

  public async htmlCreate(
    task: {
      appKey: string;
      subject: 'math' | 'chinese' | 'en-math';
      callbackUrl: string;
      priority?: number;
      extra: string;
      taskName: string;
      imageIds: string[];
      open: boolean;
      isTest: boolean;
      bookId?: number;
      bookOrder?: number;
      taskType?: ETaskType;
      timeLimit?: number;
      timeWarning?: number;
    },
    resourceType?: ETaskResourceType
  ) {
    const {
      service,
      statuses,
    } = this;
    const taskId: number = await service.task.base.getId();
    if (!taskId) {
      throw new Error('系统异常，任务创建失败');
    }
    const {
      appKey,
      subject,
      imageIds,
      priority,
      callbackUrl,
      extra,
      taskName,
      isTest,
      bookId,
      bookOrder,
      taskType = ETaskType.unset,
      timeLimit = 24 * 60,
      timeWarning = 0.6,
    } = task;
    await service.task.base.create({
      taskId,
      appKey,
      subject,
      callbackUrl,
      isTest,
      extra,
      priority,
      taskName,
      isCallback: false,
      callbackError: false,
      status: statuses.unmarked,
      errorInfo: '',
      markUserId: 0,
      reviewUserId: 0,
      splitUserId: 0,
      preprocessUserId: 0,
      operatAdminUserId: 0,
      imageCount: imageIds.length,
      catalog: '',
      bookId: bookId || 0,
      bookOrder: bookOrder == null ? 0 : bookOrder,
      taskType,
      timeLimit,
      timeWarning,
      countdown: timeLimit,
      resourceType: resourceType ?? ETaskResourceType.HTML,
    });
    const images = imageIds.map((imageId, i) => {
      return {
        taskId,
        appKey,
        priority,
        bookId: task.bookId || 0,
        imageId,
        wordCount: 0,
        marked: EImageMarkStatus.init,
        reviewed: false,
        preprocessed: true,
        disabled: false,
        filename: `${i + 1}.jpg`,
        originalId: `${imageId}.origin`,
        multiple: false,
        columnResult: '',
        taskOrder: i + 1,
        sourceId: '',
        latexResult: '',
      };
    });
    await service.image.bulkCreate(images);
    return taskId;
  }

  public async uploadImage(appKey, uri, imageId, taskId = '', ticketId = '') {
    const { logger } = this;
    let buff: Buffer | null = null;
    let href = '';
    // 拿到图片的 buffer
    if (!/^http.*$/.test(uri)) { // base64
      const base64Data = uri.indexOf(',') > -1 ? uri.split(',')[1] : uri;
      buff = Buffer.from(base64Data, 'base64');
    } else {
      try {
        /*
         * @tips：
         * urllib本身在2.33.3版本的bug https://github.com/node-modules/urllib/commit/3384e53e8447a5cb06402a43d07d7eb19a24bb62
         * 因为egg本身依赖了urllib的版本号，不便修改，暂时如此做
         * 这里用urllib 获取https://hexin-worksheet.oss-cn-shanghai.aliyuncs.com/files/9f0638154f5fbca4e0059d5fceb485fd.png这张图片获取不到 一直超时。
         * @todo：用axios替换这里的功能（@林文涛 本地用axios跑也有部分图片上传报错了，后续再改，先把线上要提的功能提上
         */
        const URL = require('url').URL;
        const url = new URL(uri as string);
        if (!url.searchParams.get('x-oss-process')) { // 旋转成正的
          // 超过 20 mb 会报错
          // url.searchParams.set('x-oss-process', 'image/auto-orient,1');
        }
        this.logger.info(`uploadImage url: taskId=${taskId}, ticketId=${ticketId}, ${url.href}, imageId=${imageId}`);
        href = url.href;
        const res = await axios.get(url.href, {
          timeout: 120 * 1000,
          responseType: 'arraybuffer',
        });
        if (!res.data || !/^2.*/.test(`${res.status}`)) {
          logger.info(`unavailable url address: ${url.href}, taskId=${taskId}, ticketId=${ticketId}, app: ${appKey}, http status: ${res.status}!`);
          return `存在无效的 url 地址: ${uri}`;
        }
        buff = res.data;
        this.logger.info('uploadImage buffer 生成: ' + imageId);
      } catch (e) {
        logger.info(`unavailable url address: ${href}, taskId=${taskId}, ticketId=${ticketId}, app : ${appKey}`, e);
        return `存在无效的 url 地址: ${uri}`;
      }
    }
    // 压缩一下图片
    try {
      // const image = sharp(buff);
      // const {
      //   width,
      //   height,
      // } = await image.metadata();
      // const limit = 2173;
      // if (Math.min(width, height) > limit) {
      //   image.resize(width < height ? { width: limit } : { height: limit });
      //   buff = await image.jpeg({ quality: 100 }).toBuffer();
      // }
      this.logger.info('uploadImage sharp 压缩: ' + imageId);
    } catch (e) {
      logger.info(`can not sharp uri: ${uri}, taskId=${taskId}, ticketId=${ticketId}, app: ${appKey}`, e);
      // @todo: 这里不知道为啥偶尔会报错，先跳过 24/02/29
      // return `无法处理的图片数据: ${uri}`;
    }
    // 上传正常的话，这里的返回值是 null
    this.logger.info('uploadImage oss 上传: ' + imageId);

    // 存一份原图
    // await this.upload(`open/${appKey}/image/${imageId}.ooriginal.jpg`, buff);
    // logger.info(`uploadImage 原图保存 open/${appKey}/image/${imageId}.ooriginal.jpg`);

    const r = await this.upload(`open/${appKey}/image/${imageId}.jpg`, buff);
    const imageUrl = this.service.image.getUrl(appKey, imageId, 'jpg');
    // 重新下载，覆盖上传
    const newBuffer = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    const rr = await this.upload(`open/${appKey}/image/${imageId}.jpg`, newBuffer.data);
    logger.info(`uploadImage success: taskId=${taskId}, ticketId=${ticketId}, app: ${appKey}, imageId=${imageId}, url=${uri}, error=${r}`);
    if (r && rr) logger.info(`uploadImage error：url is ${uri}, taskId=${taskId}, ticketId=${ticketId}, error info is ${r}`);
    return r;
  }

  /**
   * @desc - buffer上传的异常捕获，并允许三次重跑。
   * @param key - oss上传路径
   * @param buff - 上传的buffer
   * @param rerun - 重跑次数
   * @returns - string | undefined
   */
  private async upload(key: string, buff: Buffer | null, rerun = 0) {
    const {
      logger,
      service,
    } = this;
    logger.info(`service task base upload: runTime=${rerun}, key=${key}`);
    try {
      const url = await service.oss.upload(key, buff);
      if (!url) {
        if (rerun > 3) { // 重试 3 次还是不行就返回
          logger.info('service task base upload: URL 不存在');
          return 'service task base upload: URL 不存在';
        }
        return await this.upload(key, buff, rerun + 1);
      }
    } catch (e) {
      if (rerun > 3) {
        logger.info(`service task base upload：上传报错: ${e}`);
        return `service task base upload: 上传报错 ${e}`;
      }
      return await this.upload(key, buff, rerun + 1);
    }
    return; // 当前函数成功的话就返回 null 即可，有值即为异常信息
  }

  private async uploadWord(appKey: string, uri: string, taskId: number) {
    const {
      service,
      logger,
    } = this;
    let resp: urllib.HttpClientResponse<Buffer> | null;
    try {
      resp = await urllib.request(uri, {
        method: 'GET',
        timeout: 60 * 1000,
        followRedirect: true,
      });
    } catch (e) {
      logger.info(`unavailable url address : ${uri}, app : ${appKey}`, e);
      return `存在无效的url地址:${uri}`;
    }
    logger.info(`request url :${uri} : ${resp.status}`);
    if (!resp.data || !/^2.*/.test(`${resp.status}`)) {
      logger.info(`unavailbale url adress : ${uri}, app : ${appKey}, http status : ${resp.status}!`);
      return `存在无效的url地址:${uri}`;
    }
    const buffer = resp.data;
    await service.oss.upload(`open/${appKey}/task/${taskId}.origin.docx`, buffer);
  }

  private async initWordQueue(appkey: string, taskid: number, subject: 'chinese' | 'math' | 'en-math') {
    const { config } = this;
    const resp: urllib.HttpClientResponse<{ code: number; message: string }> = await urllib.request(
      config.wordRbs.initQueueUrl,
      {
        method: 'POST',
        data: {
          appkey,
          taskid,
          subject,
        },
        contentType: 'json',
        dataType: 'json',
      }
    );
    const data = resp.data;
    if (data.code !== 0) return data.message;
  }

  public async getRelatedList(options: superSequelize.GetListOptions<superSequelize.Attributes<Attributes>> = {}, pagination = true) {
    const {
      service,
      statuses,
    } = this;
    const whereOpt = options.where || {};
    this.formatWhereOpt(whereOpt);
    const appMap = {};
    console.log(whereOpt);
    const tasks = await this.getList({
      page: options.page,
      pageSize: options.pageSize,
      where: whereOpt,
      order: options.order || [['id', 'ASC']],
      attributes: [
        'taskId', 'bookId', 'taskName', 'bookOrder', 'createTime',
        'subject', 'appKey', 'imageCount', 'pdfImageCount', 'status',
        'markUserId', 'reviewUserId', 'preprocessUserId', 'splitUserId', 'operatAdminUserId',
        'errorInfo', 'isCallback', 'callbackError',
        'startMarkTime', 'endMarkTime', 'startReviewTime', 'endReviewTime',
        'rerun', 'rerunTimes', 'rerunProcessUserId',
        'taskType', 'timeLimit', 'timeWarning', 'countDown', 'warning',
        'isTest', 'resourceType', 'priority', 'parentTaskId', 'diffCharCount', 'mergedTaskId'
      ],
    }, pagination);
    if (!tasks.length) {
      return [];
    }
    const userIds: number[] = [];
    const idMap = {};
    for (const {
      reviewUserId,
      markUserId,
    } of tasks) {
      if (reviewUserId) {
        userIds.push(reviewUserId);
      }
      if (markUserId) {
        userIds.push(markUserId);
      }
    }
    // 搜索用户名
    if (userIds.length) {
      const users = await service.user.search(userIds);
      for (const {
        userId,
        nickname,
      } of users) {
        idMap[userId] = nickname;
      }
    }
    const apps = await service.appl.getAllByUc({
      where: { appKey: tasks.map((item) => item.appKey) },
      attributes: ['appKey', 'appName'],
    });
    for (const {
      appKey,
      appName,
    } of apps) {
      appMap[appKey] = appName;
    }
    tasks.forEach((task: any) => {
      task.taskCode = this.getTaskCode(task.createTime.getTime(), task.taskId);
      const hasReviewed = task.status === statuses.reviewed;
      task.originDocUrl = task.resourceType === ETaskResourceType.WORD ?
        this.getUrl(task.appKey, task.taskId, 'origin.docx', true) : null;
      task.machineUrl = (task.resourceType === ETaskResourceType.WORDV2 || task.resourceType === ETaskResourceType.FBDV2) &&
      ![statuses.init, statuses.pending, statuses.updating].includes(task.status) ? this.getUrl(task.appKey, task.taskId, 'machine.html', true) : null;
      task.docUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'docx', true) : null;
      task.htmlUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'html', true) : null;
      task.jsonUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'json', true) : null;
      task.formattedDocUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'formatted.docx', true) : null;
      task.formattedHtmlUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'formatted.html', true) : null;
      task.formattedJsonUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'formatted.json', true) : null;
      task.preprocessedJsonUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'preprocessed.json', true) : null;
      task.reviewUsername = idMap[task.reviewUserId] || '';
      task.markUsername = idMap[task.markUserId] || '';
      task.appName = appMap[task.appKey] || '';
      task.createTime = task.createTime.getTime();
      task.startMarkTime = task.startMarkTime ? Number(new Date(task.startMarkTime)) : null;
      task.endMarkTime = task.endMarkTime ? Number(new Date(task.endMarkTime)) : null;
      task.startReviewTime = task.startReviewTime ? Number(new Date(task.startReviewTime)) : null;
      task.endReviewTime = task.endReviewTime ? Number(new Date(task.endReviewTime)) : null;
    });
    return tasks;
  }

  public async getExactList(options: superSequelize.GetListOptions<superSequelize.Attributes<Attributes>> = {}, pagination = true) {
    const {
      service,
      statuses,
    } = this;
    const whereOpt = { ...options.where } || {};
    this.formatWhereOptExact(whereOpt);
    const appMap = {};
    const tasks = await this.getList({
      page: options.page,
      pageSize: options.pageSize,
      where: whereOpt,
      order: options.order || [['id', 'ASC']],
      attributes: [
        'taskId', 'bookId', 'taskName', 'bookOrder', 'createTime',
        'subject', 'appKey', 'imageCount', 'status',
        'markUserId', 'reviewUserId', 'preprocessUserId', 'splitUserId', 'operatAdminUserId',
        'errorInfo', 'isCallback', 'callbackError',
        'startMarkTime', 'endMarkTime', 'startReviewTime', 'endReviewTime',
        'rerun', 'rerunTimes', 'rerunProcessUserId',
        'taskType', 'timeLimit', 'timeWarning', 'countDown', 'warning',
        'isTest', 'resourceType', 'priority', 'parentTaskId'
      ],
    }, pagination);
    if (!tasks.length) {
      return [];
    }
    const userIds: number[] = [];
    const idMap = {};
    for (const {
      reviewUserId,
      markUserId,
    } of tasks) {
      if (reviewUserId) {
        userIds.push(reviewUserId);
      }
      if (markUserId) {
        userIds.push(markUserId);
      }
    }
    // 搜索用户名
    if (userIds.length) {
      const users = await service.user.search(userIds);
      for (const {
        userId,
        nickname,
      } of users) {
        idMap[userId] = nickname;
      }
    }
    const apps = await service.appl.getAllByUc({
      where: { appKey: tasks.map((item) => item.appKey) },
      attributes: ['appKey', 'appName'],
    });
    for (const {
      appKey,
      appName,
    } of apps) {
      appMap[appKey] = appName;
    }
    tasks.forEach((task: any) => {
      task.taskCode = this.getTaskCode(task.createTime.getTime(), task.taskId);
      const hasReviewed = task.status === statuses.reviewed;
      task.originDocUrl = task.resourceType === ETaskResourceType.WORD ?
        this.getUrl(task.appKey, task.taskId, 'origin.docx', true) : null;
      task.machineUrl = (task.resourceType === ETaskResourceType.WORDV2 || task.resourceType === ETaskResourceType.FBDV2) &&
      ![statuses.init, statuses.pending, statuses.updating].includes(task.status) ? this.getUrl(task.appKey, task.taskId, 'machine.html', true) : null;
      task.docUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'docx', true) : null;
      task.htmlUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'html', true) : null;
      task.jsonUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'json', true) : null;
      task.formattedDocUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'formatted.docx', true) : null;
      task.formattedHtmlUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'formatted.html', true) : null;
      task.formattedJsonUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'formatted.json', true) : null;
      task.preprocessedJsonUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'preprocessed.json', true) : null;
      task.reviewUsername = idMap[task.reviewUserId] || '';
      task.markUsername = idMap[task.markUserId] || '';
      task.appName = appMap[task.appKey] || '';
      task.createTime = task.createTime.getTime();
      task.startMarkTime = task.startMarkTime ? Number(new Date(task.startMarkTime)) : null;
      task.endMarkTime = task.endMarkTime ? Number(new Date(task.endMarkTime)) : null;
      task.startReviewTime = task.startReviewTime ? Number(new Date(task.startReviewTime)) : null;
      task.endReviewTime = task.endReviewTime ? Number(new Date(task.endReviewTime)) : null;
    });
    return tasks;
  }

  public async relatedCount(options: superSequelize.GetListOptions<superSequelize.Attributes<Attributes>> = {}) {
    const whereOpt = options.where || {};
    this.formatWhereOpt(whereOpt);
    return await this.count({ where: whereOpt });
  }

  public async exactCount(options: superSequelize.GetListOptions<superSequelize.Attributes<Attributes>> = {}) {
    const whereOpt = { ...options.where } || {};
    this.formatWhereOptExact(whereOpt);
    return await this.count({ where: whereOpt });
  }

  private formatWhereOptExact(whereOpt) {
    const { key } = whereOpt;
    whereOpt.taskName = key;
    delete whereOpt.key;
  }

  private formatWhereOpt(whereOpt) {
    const { key } = whereOpt;
    if (key) {
      if (/^[0-9]+$/.test(key)) {
        whereOpt.taskId = Number(key);
      } else {
        whereOpt.taskName = { $like: `%${key}%` };
      }
    }
    delete whereOpt.key;
  }

  public async getRelateOne(
    options: superSequelize.GetOneOptions<superSequelize.Attributes<Attributes>> = {},
    {
      withHtml = true,
      withUsername = true,
      withAppName = true,
    } = {}
  ) {
    const { service } = this;
    options.attributes = [
      'taskId', 'bookId', 'taskName', 'bookOrder', 'createTime',
      'subject', 'appKey', 'imageCount', 'status',
      'markUserId', 'reviewUserId', 'preprocessUserId', 'operatAdminUserId',
      'errorInfo', 'isCallback', 'callbackError',
      'startMarkTime', 'endMarkTime', 'startReviewTime', 'endReviewTime',
      'rerun', 'rerunTimes', 'rerunProcessUserId',
      'taskType', 'timeLimit', 'timeWarning', 'countDown', 'warning',
      'isTest', 'resourceType'
    ];
    const task: any = await this.getOne(options);
    if (!task) {
      return null;
    }

    task.taskCode = this.getTaskCode(task.createTime.getTime(), task.taskId);

    if (withHtml) {
      task.html = await this.getOssData(task.appKey, task.taskId, 'html');
    }
    if (withUsername) {
      // 搜索用户名
      const idMap = {};
      if (task.markUserId || task.reviewUserId) {
        const users = await service.user.search([task.markUserId, task.reviewUserId]);
        for (const {
          userId,
          nickname,
        } of users) {
          idMap[userId] = nickname;
        }
      }
      task.reviewUsername = idMap[task.reviewUserId] || '';
      task.markUsername = idMap[task.markUserId] || '';
    }
    if (withAppName) {
      const appl = await service.appl.getOneByUc({ where: { appKey: task.appKey } });
      task.appName = appl ? appl.appName : '';
    }

    const hasReviewed = task.status === this.statuses.reviewed;
    task.docUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'docx', true) : null;
    task.htmlUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'html', true) : null;
    task.jsonUrl = hasReviewed ? this.getUrl(task.appKey, task.taskId, 'json', true) : null;

    task.createTime = Number(new Date(task.createTime));
    task.startMarkTime = task.startMarkTime ? Number(new Date(task.startMarkTime)) : null;
    task.endMarkTime = task.endMarkTime ? Number(new Date(task.endMarkTime)) : null;
    task.startReviewTime = task.startReviewTime ? Number(new Date(task.startReviewTime)) : null;
    task.endReviewTime = task.endReviewTime ? Number(new Date(task.endReviewTime)) : null;

    return task;
  }

  public async pop(type: string) {
    const {
      statuses,
      app,
    } = this;
    const { literal } = app.model;
    const updateOpt = type === 'imageStructProcessor' ? { status: statuses.autoProcessing } : { status: statuses.columnAutoProcessing };
    const whereOpt = type === 'imageStructProcessor' ? {
      status: statuses.columnProcessed,
      // appKey: { $not: 'hexin' },
    } : { status: statuses.columnQueue };
    // 优先返回 rerun=true 的任务
    let task = await this.getOne({
      where: whereOpt,
      order: [[literal(`taskType=${ETaskType.limit}`) as any, 'desc'], ['rerun', 'desc']],
    });
    if (!task) {
      // 找不到等待进入划块队列 / 人工划块处理完毕 ，则找时间间隔比较久的 autoProcessing/columnAutoProcessing 状态的任务
      const timeUpWhereOptions = type === 'imageStructProcessor' ? { status: statuses.autoProcessing } : { status: statuses.columnAutoProcessing };
      task = await this.getOne({
        where: timeUpWhereOptions,
        order: [['updateTime', 'ASC'], ['rerun', 'desc']],
      });
      const taskId = task?.taskId;
      if (!task) return;
      const timeDiff = (new Date().getTime() - new Date(task!.updateTime).getTime()) / (1000 * 60);
      // 超过半小时的任务，则重新识别失败的图片
      if (timeDiff <= 360) return;
      const updateTime = dateformat(new Date(), 'yyyy-mm-dd HH:MM:ss');
      await this.update(
        {
          updateTime,
          rerun: true,
        },
        { where: { id: task.id } }
      );
      // 获取图片信息 一起把 rerun 设为true
      await this.service.image.update(
        { rerun: true },
        { where: { taskId: task.taskId } }
      );
      if (type === 'imageColumnProcessor') {
        await this.service.image.update(
          {
            preprocessed: false,
            disabled: false,
          },
          {
            where: task.rerun ?
              {
                taskId,
                rerun: task.rerun,
              } :
              { taskId },
          }
        );
        await this.service.image.delete(
          {
            where: task.rerun ?
              {
                taskId,
                originalId: { $not: '' },
                rerun: true,
              } :
              {
                taskId,
                originalId: { $not: '' },
              },
          }
        );
        // 清空 redis
        if (taskId) {
          await this.service.task.base.resetTaskConfig(taskId, 'imageColumnProcessor');
          this.logger.info(`task ${taskId} resetTaskConfig`);
          // 记录日志 - 自动重跑
          this.ctx.runInBackground(async() => {
            // 记录操作日志
            await this.service.task.history.create({
              taskId: taskId!,
              userId: this.service.user.aiFilterUserId,
              type: this.service.task.history.otherTypes.autoRestart.id,
              costTime: 0,
              data: '',
            });
          });
        }
      }
    }
    await this.update(
      updateOpt,
      { where: { id: task.id } }
    );
    return task;
  }

  public async restoreColumn(taskId: string, rerun?: boolean) {
    // 重新人工划块
    const {
      app,
      service,
    } = this;
    const { statuses } = this;
    const imageCount = await service.image.count({
      where: rerun ?
        {
          taskId,
          $or: [{
            rerun: false,
            disabled: false,
          }, {
            rerun: true,
            originalId: '',
          }],
        } as any :
        {
          taskId,
          originalId: '',
        },
    });
    await app.model.transaction(async(transaction) => {
      await service.image.delete({
        transaction,
        where: rerun ?
          {
            taskId,
            originalId: { $not: '' },
            rerun: true,
          } :
          {
            taskId,
            originalId: { $not: '' },
          },
      });
      await service.image.update(
        {
          disabled: false,
          preprocessed: false,
          marked: EImageMarkStatus.init,
          reviewed: false,
        },
        {
          transaction,
          where: rerun ?
            {
              taskId,
              originalId: '',
              rerun: true,
            } :
            {
              taskId,
              originalId: '',
            },
        }
      );
      await this.update(
        {
          status: statuses.columnAutoProcessed,
          imageCount,
          [rerun ? 'rerunProcessUserId' : 'preprocessUserId']: 0,
          markUserId: rerun ? undefined : 0,
          reviewUserId: 0,
          isCallback: false,
          callbackError: false,
          startMarkTime: rerun ? undefined : null,
          endMarkTime: rerun ? undefined : null,
          startReviewTime: null,
          endReviewTime: null,
        },
        {
          transaction,
          where: { taskId },
        }
      );
    });
  }

  public async initTaskConfig(
    taskId: number,
    type: TaskProcessTypes,
    map: { [imageId: string]: number }
  ) {
    if (!Object.keys(map).length) return;
    await this.app.redis.hmset(this.getTaskConfigKey(taskId, type), map);
  }

  public async getTaskConfig(taskId: number, type: TaskProcessTypes) {
    return await this.app.redis.hgetall(this.getTaskConfigKey(taskId, type));
  }

  public async getTaskStatus(taskId: number, type: TaskProcessTypes, imageId: string) {
    return await this.app.redis.hget(this.getTaskConfigKey(taskId, type), imageId);
  }

  public async setTaskStatus(taskId: number, type: TaskProcessTypes, imageId: string) {
    await this.app.redis.hset(this.getTaskConfigKey(taskId, type), imageId, '1');
  }

  public async hasAllCompleted(taskId: number, type: TaskProcessTypes) {
    const data = await this.getTaskConfig(taskId, type);
    let count = 0;
    this.logger.info(`hasAllCompleted taskId:${taskId} data:${JSON.stringify(data)}`);
    Object.values(data).forEach((item) => {
      if (item === '1') {
        count += 1;
      }
    });
    return count === Object.keys(data).length;
  }

  public async deleteTaskConfig(taskId: number, type: TaskProcessTypes) {
    await this.app.redis.del(this.getTaskConfigKey(taskId, type));
  }

  /**
   * 重置taskConfig队列（划块 & 内容识别队列）
   * @param taskId
   * @param type
   * @param imageMap { [key: string]: number }
   */
  public async resetTaskConfig(taskId: number, type: TaskProcessTypes, imageMap = {}) {
    await this.deleteTaskConfig(taskId, type);
    if (!Object.keys(imageMap).length) {
      const images = await this.service.image.getAll({
        where: {
          taskId,
          disabled: false,
        },
        attributes: ['imageId'],
        order: [['id', 'ASC']],
      });
      images.forEach((item) => {
        imageMap[item.imageId] = 0;
      });
    }
    return await this.initTaskConfig(taskId, type, imageMap);
  }

  private async getImageBuffer(appKey: string, imageId: string) {
    const {
      service,
      ctx,
    } = this;
    const url = service.image.getUrl(appKey, imageId, 'jpg', false, true);
    const resp = await ctx.curl(url);
    if (resp.status && !/^([23]).*/.test(`${resp.status}`)) {
      throw new Error(`获取图片错误 ${resp.status}`);
    }
    return resp.data as Buffer;
  }

  private async getSplitImageUrl(appKey: string, imageId: string, result: {
    x: number,
    y: number,
    w: number,
    h: number,
    i?: number
  }) {
    const { config } = this;
    const {
      x,
      y,
      w,
      h,
    } = result;
    const url = `${config.aliOss.privateHost}open/${appKey}/image/${imageId}.jpg?x-oss-process=image/crop,x_${x},y_${y},w_${w},h_${h}`;
    return url;
  }

  private newImageFromSource(taskId: number, sourceImage: SourceImageModel.Instance, rerun = false) {
    return {
      taskId,
      imageId: sourceImage.imageId,
      appKey: sourceImage.appKey,
      wordCount: 0,
      marked: EImageMarkStatus.init,
      reviewed: false,
      preprocessed: false,
      disabled: false,
      filename: sourceImage.filename || '',
      bookId: sourceImage.bookId || 0,
      taskOrder: sourceImage.taskOrder,
      originalId: '',
      sourceId: sourceImage.imageId,
      multiple: false,
      columnResult: '',
      latexResult: '',
      rerun,
    } as ImageModel.Attributes;
  }

  public async rotatePreImage(appKey: string, imageId: string, angle: 90 | -90 | 180) {
    const {
      service,
      logger,
    } = this;
    let buffer = await this.getImageBuffer(appKey, imageId);
    const image = sharp(buffer);
    image.rotate(angle);

    buffer = await image.jpeg({ quality: 100 }).toBuffer();
    await service.oss.upload(`open/${appKey}/image/${imageId}.jpg`, buffer);
    try {
      const info = await service.image.getImageInfo(appKey, imageId);
      await service.sourceImage.update({
        info: JSON.stringify(info),
        result: '',
      }, { where: { imageId } });
    } catch (e) {
      logger.error('获取图片信息错误', e);
    }
  }

  public async rotatePdfImage(imageId: string, angle: 90 | -90 | 180, appKey: string) {
    const {
      service,
      logger,
    } = this;
    let buffer = await this.getImageBuffer(appKey, imageId);
    const image = sharp(buffer);
    image.rotate(angle);

    buffer = await image.jpeg({ quality: 100 }).toBuffer();
    await service.oss.upload(`open/${appKey}/image/${imageId}.jpg`, buffer);
    logger.info(`rotatePdfImage to xdoc-oss success url: open/${appKey}/image/${imageId}.jpg`);
  }

  public async uploadSplitImg(appKey: string, imageId: string, result: {
    x: number,
    y: number,
    w: number,
    h: number,
    i?: number
  }[]) {
    return await Promise.all(
      result!.map(async(item) => {
        const url = await this.getSplitImageUrl(appKey, imageId, item);
        const newImageId = uuid().replace(/-/g, '') as string;
        await this.uploadImage(
          appKey,
          url,
          newImageId
        );
        const info = await this.service.image.getImageInfo(
          appKey,
          newImageId
        );
        return {
          newImageId,
          info,
        };
      })
    );
  }

  /**
   * 结束预切图 生成 image 图片 进入划块队列
   * @param taskId
   * @param sourceImages
   * @param rerun 重跑的任务，需要让生成的图片带上rerun字段
   */
  public async finishPreCropImage(taskId: number, sourceImages: SourceImageModel.Instance[], rerun = false) {
    const { service } = this;
    const { statuses } = this;
    let images: ImageModel.Attributes[] = [];
    let sizeLimit = 1536;

    function resizeMaxSize(image: any, {
      w,
      h,
    }: { w: number; h: number }) {
      // 如果边长大于 1536，进行压缩
      if (w > sizeLimit || h > sizeLimit) { // 需要resize
        const opt = w > h ? { width: sizeLimit } : { height: sizeLimit };
        image.resize(opt);
      }
    }

    async function compressUpload(image: any, appKey: string) {
      // jpeg 格式上传 oss
      const imageId = uuid().replace(/-/g, '');
      const buffer = await image.jpeg({ quality: 100 }).toBuffer();
      await service.oss.upload(`open/${appKey}/image/${imageId}.jpg`, buffer);
      return imageId;
    }

    const task = await service.task.base.getOne({
      where: { taskId }
    });
    if (!task) {
      this.logger.error(`finishPreCropImage taskId:${taskId} not found`);
      return;
    }
    let isTest = ['5d0b7d5c7d35cb32', 'fc7539b21810cd4f0f0fb620'].includes(task.appKey);
    if (isTest) {
      sizeLimit = 99999;
    };
    this.logger.info(`finishPreCropImage taskId:${taskId} isTest:${isTest} sizeLimit:${sizeLimit}`);
    let hasRects = false;
    let sourceImageIndex = 0;
    this.logger.info(`finishPreCropImage begin taskId:${taskId} sourceImages:${sourceImages.length && sourceImages.map((item) => item.imageId)}`);
    for (const sourceImage of sourceImages) {
      const result = sourceImage.result ?
        JSON.parse(sourceImage.result) as { x: number; y: number; w: number; h: number; i?: number; gi?: number }[] :
        undefined; // 切图信息
      // 图片大小信息，相机朝向信息
      const info = JSON.parse(sourceImage.info) as { w: number; h: number; o?: number; f?: string };
      // 需要 resize 或 需要抹掉朝向信息 或 需要格式化。o(orientation) >=5 表示相机横向拍照；f(format) 是图片格式
      const shouldSharp = info.w > sizeLimit || info.h > sizeLimit || info.o! >= 5 || info.f !== 'jpg';
      this.logger.info(`finishPreCropImage taskId:${taskId} result:${result}`);
      if (!result) { // 无切图
        const imageData = this.newImageFromSource(taskId, sourceImage, rerun);
        Object.assign(imageData, { i: sourceImageIndex * 100 });
        if (shouldSharp) {
          const buffer = await this.getImageBuffer(sourceImage.appKey, sourceImage.imageId);
          const image = sharp(buffer); // sharp 自动处理朝向
          resizeMaxSize(image, info);
          imageData.imageId = await compressUpload(image, imageData.appKey);
          this.logger.info(`finishPreCropImage skip resize ${taskId}`);
        }
        images.push(imageData);
      } else {
        hasRects = true;
        const rects = result.filter((r) => r.i != null && r.i >= 0); // 过滤禁用切块
        let image;
        const rect = rects[0];
        // 只有1个全图切块
        const isFullRect = rects.length === 1 &&
          (rect.x === 0 && rect.y === 0 && rect.w === info.w && rect.h === info.h);
        // this.logger.info(`[finishPreCropImage] rects=${rects} appKey=${sourceImage.appKey} imageId=${sourceImage.imageId}`);
        if (!isFullRect || shouldSharp) {
          const buffer = await this.getImageBuffer(sourceImage.appKey, sourceImage.imageId);
          image = sharp(buffer);
        }
        let rectIndex = 0;
        for (const rect of rects) {
          const imageData = this.newImageFromSource(taskId, sourceImage, rerun);
          imageData.filename = service.image.setFileOrder(imageData.filename, rectIndex + 1);
          Object.assign(imageData, { i: rect.gi == null ? sourceImageIndex * 100 + rect.i! : rect.gi });
          if (!isFullRect) {
            let rectImage = image.clone();
            /*
             * Tips：
             * 考虑图片可能存在旋转的情况，先旋转、否则宽高可能是相反的，
             * 可以参考文章 https://www.jianshu.com/p/3c872fc41e7f
             */
            if (info.o && info.o >= 5) {
              rectImage = rectImage.rotate();
            }
            /*
             * const { orientation, width, height } = await rectImage.metadata();
             * this.logger.info(`[finishPreCropImage] orientation=${orientation} width=${width} height=${height}`);
             */
            rectImage = rectImage.extract({
              left: Math.ceil(rect.x),
              top: Math.ceil(rect.y),
              width: Math.ceil(rect.w),
              height: Math.ceil(rect.h),
            });
            resizeMaxSize(rectImage, rect);
            imageData.imageId = await compressUpload(rectImage, imageData.appKey);
          } else if (shouldSharp) {
            // 暂时跳过压缩
            resizeMaxSize(image, rect);
            imageData.imageId = await compressUpload(image, imageData.appKey);
          } else {
            // 全图切块，且不需要压缩。使用原图
          }
          images.push(imageData);
          rectIndex += 1;
        }
      }

      sourceImageIndex += 1;
      this.logger.info(`finishPreCropImage taskId:${taskId} sourceImage:${sourceImage.imageId} finish`);
    }
    if (hasRects) { // 有切块信息，按切块顺序排序
      images = _.orderBy(images, (item: any) => item.i) as any;
      images.forEach((item, index) => {
        item.taskOrder = index;
        delete (item as any).i;
      });
    }
    this.logger.info(`finishPreCropImage taskId:${taskId} finish`);
    /*
     * Tips：
     * 根据 sourceImage 表创建 image 数据，
     * 这里在创建前先清理一下旧数据，避免反复重跑会遗留很多脏数据，
     * 真删。
     */
    await service.image.forceDelete({ where: { taskId } });
    if (isTest) {
      // 这里改为 word 任务
      const book = await service.book.getOne({ where: { id: task.bookId } });
      this.logger.info(`finishPreCropImage taskId:${taskId} book:${JSON.stringify(book)}`);
      const project = await service.project.base.getOne({ where: { id: book!.projectId } });
      this.logger.info(`finishPreCropImage taskId:${taskId} project:${JSON.stringify(project)}`);
      const taskMeta = await service.task.meta.getMetas({ taskId });
      this.logger.info(`finishPreCropImage taskId:${taskId} taskMeta:${JSON.stringify(taskMeta)}`);
      const imageUrlList = images.map((item) => this.service.image.getUrl(item.appKey, item.imageId, 'jpg', true));
      this.logger.info(`finishPreCropImage taskId:${taskId} imageUrlList:${JSON.stringify(imageUrlList)}`);
      this.logger.info(`finishPreCropImage taskId:${taskId} params:${JSON.stringify({
        task_id: taskId.toString(),
        image_url_list: imageUrlList,
        app_key: task.appKey,
        stage: taskMeta.stage,
        subject: task.subject,
        task_name: task.taskName,
        ticket_id: project?.workOrder || '',
        project_id: project?.id || '',
      })}`);

      // 入队 image2html
      await this.service.rbs.initRBSQueue({
        task_id: taskId.toString(),
        task_type: 'image2html',
        task_info: {
          task_id: taskId.toString(),
          image_url_list: imageUrlList,
          app_key: task.appKey,
          stage: taskMeta.stage,
          subject: task.subject,
          task_name: task.taskName,
          ticket_id: project?.workOrder || '',
          project_id: project?.id || '',
          get_task_parameters:[
            'task_id',
            'image_url_list',
            'app_key',
            'stage',
            'subject',
            'task_name',
            'ticket_id',
            'project_id'
          ],
          run_type: 'common',
          push_time: new Date().getTime(),
          timestamp: new Date().getTime(),
        },
      });
      this.logger.info(`finishPreCropImage taskId:${taskId} image2html queue`);
      return;
    }

    await service.image.bulkCreate(images);
    const imageMap = {};
    images.forEach((item) => {
      imageMap[item.imageId] = 0;
    });
    this.logger.info(`finishPreCropImage taskId:${taskId} finish imageSort`);

    await this.update({
      status: statuses.columnQueue,
      imageCount: images.length,
    }, { where: { taskId } });

    /*
     * Tips：
     * 在 Redis 中创建任务的配置，
     * key 是图片 id，value 是算法侧是否完成，
     * 在后面的逻辑中会判断当全部图片的 value 都为 1 时，表示任务（机器识别）处理完成。
     * 结束预切图 清空redis队列
     */
    await this.resetTaskConfig(taskId, 'imageColumnProcessor', imageMap);
  }

  public readonly publishKey = 'xdoc:task:publish:queue';

  public async pushToPublish(...taskIds: number[]) {
    await this.app.redis.lpush(this.publishKey, ...taskIds);
  }

  public async popFromPublish() {
    const taskId = await this.app.redis.rpop(this.publishKey);
    return Number(taskId) || 0;
  }

  public readonly preCropCheckKey = 'xdoc:task:pre_crop_check:list';

  public async pushPreCropCheck(tasks: any[], info: { type: string }) {
    // @tips: 拦截一下，这里区分 v1/v2 任务, v2 直接走 rbs 新算法
    const { type } = info;
    this.logger.info(`[pushPreCropCheck] info: ${JSON.stringify(info)}`);
    this.logger.info(`[pushPreCropCheck] info: ${JSON.stringify(tasks)}`);
    if (type === 'v2') {
      // rbs
      for (const task of tasks) {
        const sourceImageIds = await this.service.sourceImage.getAll({
          where: { taskId: task.taskId },
          attributes: ['imageId'],
        });

        const images = sourceImageIds.map((item, i) => {
          return {
            taskId: task.taskId,
            imageId: item.imageId,
            appKey: task.appKey,
            wordCount: 0,
            marked: EImageMarkStatus.init,
            reviewed: false,
            preprocessed: true,
            disabled: false,
            filename: `${i + 1}.jpg`,
            bookId: task.bookId,
            taskOrder: i + 1,
            originalId: '',
            sourceId: '',
            multiple: false,
            columnResult: '',
            latexResult: '',
          } as ImageModel.Attributes;
        });
        await this.service.image.bulkCreate(images);
        this.logger.info(`[pushPreCropCheck] task_id: ${task.taskId} image count: ${images.length} images: ${JSON.stringify(images)}}`);

        const imageUrls = images.map((item) => this.service.image.getUrl(task.appKey, item.imageId, 'jpg', false, false));
        // 限制图片的大小，最大的边像素不要超过 1024
        this.logger.info(`[pushPreCropCheck] task_id: ${task.taskId} imageUrls: ${JSON.stringify(imageUrls)}`);
        await this.service.rbs.initRBSQueue({
          task_id: task.taskId.toString(),
          task_type: 'image2html',
          task_info: {
            task_id: task.taskId.toString(),
            subject: task.subject,
            image_urls: imageUrls,
            get_task_parameters: [
              'task_id',
              'subject',
              'image_urls'
            ],
            callback_extras: [
              'task_id',
              'subject',
              'image_urls'
            ],
            run_type: 'common',
            callback_url: 'http://xdoc.open.hexinedu.com/api/open/task/updateV2',
            push_time: new Date().getTime(),
            timestamp: new Date().getTime(),
          },
        });
        this.logger.info(`[pushPreCropCheck] task_id: ${task.taskId} task_type: image2html`);
      }
    } else {
      await this.app.redis.lpush(this.preCropCheckKey, ...tasks.map((task) => task.taskId));
    }
  }

  public async popPreCropCheck() {
    const taskId = await this.app.redis.rpop(this.preCropCheckKey);
    return Number(taskId) || 0;
  }

  public readonly preCropKey = 'xdoc:task:pre_crop:list';

  public async pushPreCrop(...taskIds: number[]) {
    await this.app.redis.lpush(this.preCropKey, ...taskIds);
  }

  public async popPreCrop() {
    const taskId = await this.app.redis.rpop(this.preCropKey);
    return Number(taskId) || 0;
  }

  public readonly cleanDataKey = 'xdoc:task:clean_data:list';

  public async pushCleanData(...taskIds: number[]) {
    await this.app.redis.lpush(this.cleanDataKey, ...taskIds);
  }

  public async popCleanData() {
    const taskId = await this.app.redis.rpop(this.cleanDataKey);
    return Number(taskId) || 0;
  }

  public getTaskCode(createTime: number, taskId: number) {
    const left = createTime.toString(36).substring(1).padEnd(9, '0');
    const right = taskId.toString(36).padStart(9, '0');
    return `${left}${right}`.toUpperCase();
  }

  public async getTaskByCode(
    code: string,
    options: superSequelize.GetOneOptions<superSequelize.Attributes<Attributes>> = {}
  ) {
    const left = code.substring(0, 9);
    const right = code.substring(9);
    const taskId = parseInt(right, 36);
    if (!options.where) {
      options.where = {};
    }
    options.where.taskId = taskId;
    if (options.attributes && Array.isArray(options.attributes)) {
      options.attributes = [...options.attributes, 'createTime'];
    }
    const task = await this.getOne(options);
    if (!task) {
      return;
    }
    const createTime = task.createTime as any as Date;
    if (left.toLowerCase() !== createTime.getTime().toString(36).substring(1).padEnd(9, '0')) {
      return;
    }
    return task;
  }

  public async updateTaskCountdown(where: sequelize.WhereOptions<Attributes> = {}) {
    // 更新任务倒计时和预警
    const { statuses } = this;
    Object.assign(where, {
      taskType: ETaskType.limit,
      status: { $ne: statuses.reviewed },
    });
    const tasks = await this.getAll({
      where,
      attributes: ['taskId', 'createTime', 'timeLimit', 'timeWarning'],
    });
    if (tasks.length) {
      const now = new Date().getTime();
      tasks.forEach((task) => {
        const createTime = task.createTime as any as Date;
        task.countdown = task.timeLimit! - Math.round((now - createTime.getTime()) / 1000 / 60);
        task.warning = task.countdown / task.timeLimit! <= task.timeWarning!;
        delete task.createTime;
        delete task.timeLimit;
        delete task.timeWarning;
      });
      await this.bulkCreate(tasks, { updateOnDuplicate: ['countdown', 'warning'] });
    }
    return tasks.length;
  }

  public async formateAndUploadDiffFile(appKey: string, taskId: number, html: string) {
    await this.formateAndUploadDiffJsonHtml(appKey, taskId, html, 'diff.json.html');
    await this.formateAndUploadDiffDocxHtml(appKey, taskId, html, 'diff.docx.html');
    const json = await this.convert2Json(appKey, taskId, this.clean2JsonHtml(html));
    await this.formateAndUploadDiffJson(appKey, taskId, json, 'diff.json');
  }

  public async formateAndUploadDiffFormattedFile(appKey: string, taskId: number, html: string) {
    await this.formateAndUploadDiffJsonHtml(appKey, taskId, html, 'diff.json.formatted.html');
    await this.formateAndUploadDiffDocxHtml(appKey, taskId, html, 'diff.docx.formatted.html');
    const json = await this.convert2Json(appKey, taskId, this.clean2JsonHtml(html));
    await this.formateAndUploadDiffJson(appKey, taskId, json, 'diff.formatted.json');
  }

  public clean2JsonHtml(html: string) {
    const nodes = parseHtml(html);
    for (const {
      node,
      index,
      parent,
    } of iterateNode(nodes)) {
      if (node.tagName === 'span' && (node.dataset.label === 'rect' || !node.dataset.label)) { // 删除无意义的span标签
        (parent as any)?.children.splice(index, 1, {
          type: 'text',
          content: '',
        }, ...node.children);
      }
    }
    return getHtml(nodes).replace(/data-firebase-message=".*?"/g, '');
  }

  public async formateAndUploadDiffJsonHtml(appKey: string, taskId: number, html: string, extension: supportExtension) {
    const diffHtmlArr = html.split(/<hr[^>]*? data-label="answer_separator" ?\/?>/);
    if (diffHtmlArr.length === 2) { // 删除答案部分的题号和标题
      diffHtmlArr[1] = diffHtmlArr[1]
        .replace(/(<p[^>]*? data-label="header"[^>]*?>[^<]*?<\/p>)/g, '')
        .replace(/(<span[^>]*? data-label="quest_num"[^>]*?>[^<]*?<\/span>)/g, '');
    }
    const diffHtml = diffHtmlArr.join('')
      .replace(/data-firebase-message=".*?"/g, '') // 移除防火墙信息
      .replace(/<table[^>]*>(\s*<tr>\s*<\/tr>)*\s*<\/table>/g, '') // 移除空白表格（没有单元格）（无 tbody 情况）
      .replace(/<table[^>]*>\s*<tbody>(\s*<tr>\s*<\/tr>)*\s*<\/tbody>\s*<\/table>/g, ''); // 移除空白表格（没有单元格）;
    const nodes = parseHtml(diffHtml);
    for (const {
      node,
      index,
      parent,
    } of iterateNode(nodes)) {
      if (node.type === 'element' && node.tagName === 'span' && node.dataset.label === 'quest_num') { // 删除题号上的style
        delete node.attrs.style;
      }
      if (
        node.type === 'element' && node.tagName === 'span' && node.dataset.label === 'quest_num' &&
        parent?.tagName === 'p' && (parent?.dataset?.label === 'answer' || parent?.dataset?.label === 'explanation')
      ) { // 清除答案中的题号
        (parent as any)?.children.splice(index, 1, {
          type: 'text',
          content: '',
        });
      }
      if (node.tagName === 'span' && (node.dataset.label === 'rect' || !node.dataset.label)) { // 删除无意义的span标签
        (parent as any)?.children.splice(index, 1, {
          type: 'text',
          content: '',
        }, ...node.children);
      }
    }
    await this.setOssData(appKey, taskId, extension, getHtml(nodes));
  }

  public async formateAndUploadDiffDocxHtml(appKey: string, taskId: number, html: string, extension: supportExtension) {
    const diffHtml = html.replace(/data-firebase-message=".*?"/g, '') // 移除防火墙信息
      .replace(/aria-label=".*?"/g, '') // 不知道哪来的图说小部件
      .replace(/<table[^>]*>(\s*<tr>\s*<\/tr>)*\s*<\/table>/g, '') // 移除空白表格（没有单元格）（无 tbody 情况）
      .replace(/<table[^>]*>\s*<tbody>(\s*<tr>\s*<\/tr>)*\s*<\/tbody>\s*<\/table>/g, ''); // 移除空白表格（没有单元格）;
    const nodes = parseHtml(diffHtml);
    for (const { node } of iterateNode(nodes)) {
      if (
        node.type === 'element' && node.tagName === 'img' && node.dataset.description
      ) { // 清除图说
        delete node.dataset.description;
        delete node.attrs['data-description'];
      }
      if (node.type === 'element' && node.attrs.style) { // 清除style标签
        delete node.attrs.style;
      }
    }
    await this.setOssData(appKey, taskId, extension, getHtml(nodes));
  }

  public async formateAndUploadDiffJson(appKey: string, taskId: number, json: TJsonNode[], extension: supportExtension) {
    const diffJson = cleanJsonNodes(json)!;
    for (const { node } of iterateNode(diffJson)) {
      if (node.node_type === 'question' && node.question_type === 'material') {
        node.content.serial_number = '';
      }
      if (node.node_type === 'chapter') {
        node.node_name = '';
      }
    }
    await this.setOssData(appKey, taskId, extension, diffJson);
  }

  public judgeOpenTaskStatus(taskStatus: number) {
    const {
      statuses,
      openStatuses,
    } = this.service.task.base;

    let status = openStatuses.init;
    const {
      columnProcessing,
      columnProcessed,
      autoProcessing,
      unmarked,
      marking,
      unreviewed,
      reviewing,
      dataCleaning,
      dataCleanfailed,
      jsonPreProcessing,
      reviewed,
      operatAdmin,
      closed,
    } = statuses;
    if ([
      columnProcessing,
      columnProcessed,
      autoProcessing,
      unmarked,
      marking,
      unreviewed,
      reviewing,
      dataCleaning,
      dataCleanfailed,
      jsonPreProcessing,
      reviewed,
      operatAdmin
    ].includes(taskStatus)) {
      status = openStatuses.processing;
    }
    if (taskStatus === closed) {
      status = openStatuses.revoked;
    }

    if (taskStatus === reviewed) {
      status = openStatuses.successful;
    }
    if ([statuses.error].includes(taskStatus)) {
      status = openStatuses.failed;
    }
    return status;

  }

  public async createMergedTask(_bookOrder, tasks: Task[], service, transaction: sequelize.Transaction, newTaskIds: number[], taskId: number, appKey, subject, task, status: number, bookId, resourceType, logger, book, file: Partial<ITaskFiles>, path) {
    _bookOrder += 1;
    const _html = tasks.sort((a, b) => a.bookOrder - b.bookOrder).map((t) => t.html).join('');
    const mergedMeta: any = tasks.map((_task) => _task.meta);
    const id = await service.task.base.getBatchIds(1);
    // 更新 meta 数据， 这里需要是个数组。
    await service.task.meta.setMetas([id[0]], {
      ...mergedMeta,
      path: path.toString(),
    }, { transaction });
    newTaskIds.push(id[0]);
    if (id.length !== 1) {
      throw new Error('service.taskV2.splitCreate.if: taskIds.length不对');
    }
    // 创建新任务
    try {
      await this.create({
        parentTaskId: taskId,
        taskId: Number(id[0]),
        appKey,
        subject,
        callbackUrl: '',
        isTest: false,
        priority: task.priority,
        extra: '',
        taskName: tasks[0].name,
        isCallback: false,
        callbackError: false,
        status,
        errorInfo: '',
        markUserId: 0,
        reviewUserId: 0,
        splitUserId: 0,
        preprocessUserId: 0,
        operatAdminUserId: 0,
        imageCount: 0,
        catalog: '',
        bookId,
        bookOrder: _bookOrder,
        taskType: ETaskType.unset,
        timeLimit: 24 * 60,
        timeWarning: 0.6,
        countdown: 24 * 60,
        resourceType,
      }, { transaction });
    } catch (e) {
      logger.info('split.confirm.runInBackground create new task error ');
    }

    try {
      for (const t of tasks) {
        const index = tasks.indexOf(t);
        await this.update({
          mergedTaskId: id[0],
          mergedOrder: index + 1,
        }, {
          where: { taskId: t.taskId },
          transaction,
        });
        await service.task.file.setFiles([id[0]], file, { transaction });
      }
    } catch (e) {
      logger.info('split.confirm.runInBackground update old task error ');
    }

    const ossClient = this.service.oss.createOss();
    const sourceKey = service.project.base.getOssKey(book!.appKey, book!.projectId, 'meta.json');
    const key = this.getOssKey(book!.appKey, id[0], 'meta.json');
    try {
      await ossClient.copy(key, sourceKey);
    } catch (e) {
      logger.info('split.confirm.runInBackground set child step2 setmeta error ');
    }
    await this.setOssData(book!.appKey, id[0], 'html', _html);
    await this.copyOssData(book!.appKey, id[0], 'html', 'machine.html');
    if (![ETaskResourceType.IMAGE, ETaskResourceType.HTML].includes(task.resourceType)) {
      try {
        if (file.pdfs) {
          const pdfs = file.pdfs;
          logger.info('pdfs', pdfs);
          await Promise.all(pdfs.map(async(pdf: { id: string; name: string; }) => {
            return await ossClient.copy(
              this.getOssKey(book!.appKey, `${id[0]}.${pdf.id}`, 'pdf'),
              this.getOssKey(appKey, `${taskId}.${pdf.id}`, 'pdf')
            );
          }));
        } else {
          await ossClient.copy(service.task.base.getOssKey(book!.appKey, id[0], 'pdf'), service.task.base.getOssKey(appKey, taskId, 'pdf'));
        }
      } catch (e) {
        logger.info('split.confirm.runInBackground set child step3 setpdf error ');
      }
    }
    return _bookOrder;
  }

  public async getOriginTasks(taskId: number) {
    const { service } = this;
    const tasks = await service.task.base.getAll({
      where: { mergedTaskId: taskId },
      order: [['mergedOrder', 'ASC']],
    });
    return tasks;
  }

  public splitHtml(html: string) {
    const regex = /(<hr data-label="mergeStart"[^>]*>)([\s\S]*?)(<hr data-label="mergeEnd"[^>]*>)/g;
    const intervals: string[] = [];
    let match: RegExpExecArray | null;

    while ((match = regex.exec(html)) !== null) {
      intervals.push(match[0]);
    }
    return intervals;
  }

  public async isV3(taskId: number) {
    const { service } = this;
    const task = await service.task.base.getOne({ where: { taskId } });
    if (!task) return false;
    const book = await service.book.getOne({ where: { id: task.bookId } });
    if (!book) return false;
    const projectId = book.projectId;
    const project = await service.project.base.getOne({ where: { id: projectId } });
    if (!project) return false;
    const meta = await service.project.meta.getMetas({ projectId });
    if (!meta) return false;
    return meta.isNewFlow;

  }

  public readonly regenerateImagesKey = 'xdoc:task:generate_images:list';

  public async pushToGenerateImages(inputData: {
    taskId: number;
    index: number;
    body?: string;
    level?: number;
    userId: number;
    type: 'refreshChapter' | 'changeChapter'
  }) {
    await this.app.redis.lpush(this.regenerateImagesKey, JSON.stringify(inputData));
  }

  public async popFromGenerateImages(): Promise<{
    taskId: number;
    index: number;
    body?: string;
    level?: number;
    userId: number;
    type: 'refreshChapter' | 'changeChapter'
  }> {
    const inputData = await this.app.redis.rpop(this.regenerateImagesKey);
    return JSON.parse(inputData || '{}');
  }

  /**
   * 脏表格转换
   * @param html
   */
  public async convertErrorTable(html: string) {
    try {
      const res = await axios.post(`${this.config.content.api}/open/html2html`, { html }, { headers: { 'x-request-from': 'hexin' } });
      this.logger.info('convertErrorTable', JSON.stringify(res.data));
      return res.data.data;
    } catch (e) {
      return '';
    }
  }

  public getTaskParamsKey(taskId: number) {
    return `xdoc:taskParams:${taskId}`;
  }

  private taskParamsIdKey = 'xdoc:taskParams:taskId';

  /**
   * 任务参数设置
   */
  async saveTaskParams(taskId: number, paramsString: string) {
    const { app } = this;
    await app.redis.set(this.getTaskParamsKey(taskId), paramsString);
    // await app.redis.lpush(this.taskParamsIdKey, taskId.toString());
  }

  async getTaskParamsTaskId() {
    const { app } = this;
    const taskIdInString = await app.redis.rpop(this.taskParamsIdKey);
    return Number(taskIdInString);
  }

  async getTaskParams(taskId: number) {
    const { app } = this;
    const res = await app.redis.get(this.getTaskParamsKey(taskId));
    return JSON.parse(res || '{}');
  }

  async deleteTaskParams(taskId: number) {
    const { app } = this;
    await app.redis.del(this.getTaskParamsKey(taskId));
  }

  async getRecognitionProgress(taskId: number) {
    const { app } = this;
    const res = await app.redis.hgetall(`task:${taskId}:imageStructProcessor:config`);
    return res;
  }

  async copyTask(task: any, bookId: number) {
    const { service } = this;
    if (!task) {
      return;
    }
    const book = await service.book.getOne({ where: { id: bookId } });
    if (!book) {
      return;
    }
    const project = await service.project.base.getOne({ where: { id: book.projectId } });
    if (!project) {
      return;
    }
    const meta = await service.project.meta.getMetas({ projectId: project.id });
    if (!meta) {
      return;
    }
    const newTaskId = await service.task.base.getBatchIds(1);
    this.logger.info('newTaskId', newTaskId);
    this.logger.info('oldTaskId', task.taskId);
    const newTask = {
      ...task,
      id: undefined,
      taskId: newTaskId[0],
      bookId,
      // copiedFromTaskId: task.taskId,
      taskName: `${task.taskName}-复制`,
    };
    try {
      await service.task.base.create(newTask);
    } catch (e) {
      this.logger.error('copyTask create task error', e);
      throw e;
    }
    // await service.task.meta.setMetas([newTaskId[0]], {...meta});
    // 转存文件
    const ossClient = service.oss.createOss();
    const fileList = await ossClient.list({ prefix: `open/${task.appKey}/task/${task.taskId}` });
    for (const file of fileList.objects) {
      const key = file.name.replace(`open/${task.appKey}/task/${task.taskId}`,
        `open/${task.appKey}/task/${newTaskId[0]}`);
      await ossClient.copy(key, file.name);
    }
    this.logger.info('copyTask copy file success: ' + newTaskId[0]);

    // 复制 image
    const images = await service.image.getAll({ where: { taskId: task.taskId } });
    const newImages = images.map((image) => {
      return {
        ...image,
        id: undefined,
        taskId: newTaskId[0],
      };
    });
    try {
      await service.image.bulkCreate(newImages);
    } catch (e) {
      this.logger.error('copyTask copy image error', e);
      throw e;
    }
    this.logger.info('copyTask copy image success: ' + newTaskId[0]);

    // 复制 source_image
    const sourceImages = await service.sourceImage.getAll({ where: { taskId: task.taskId } });
    const newSourceImages = sourceImages.map((sourceImage) => {
      return {
        ...sourceImage,
        id: undefined,
        taskId: newTaskId[0],
      };
    });
    try {
      await service.sourceImage.bulkCreate(newSourceImages);
    } catch (e) {
      this.logger.error('copyTask copy source_image error: ', e);
      throw e;
    }
    this.logger.info('copyTask copy source_image success: ' + newTaskId[0]);

    // 复制 task_file
    const taskFiles = await service.task.file.getAll({ where: { taskId: task.taskId } });
    const newTaskFiles = taskFiles.map((taskFile) => {
      return {
        ...taskFile,
        id: undefined,
        taskId: newTaskId[0],
      };
    });

    try {
      await service.task.file.bulkCreate(newTaskFiles);
    } catch (e) {
      this.logger.error('copyTask copy task_file error: ', e);
      throw e;
    }
    this.logger.info('copyTask copy task_file success: ' + newTaskId[0]);

    // 复制 task_meta
    const taskMetas = await service.task.meta.getAll({ where: { taskId: task.taskId } });
    const newTaskMetas = taskMetas.map((taskMeta) => {
      return {
        ...taskMeta,
        id: undefined,
        taskId: newTaskId[0],
      };
    });
    try {
      await service.task.meta.bulkCreate(newTaskMetas);
    } catch (e) {
      this.logger.error('copyTask copy task_meta error', e);
      throw e;
    }
    this.logger.info('copyTask copy task_meta success: ' + newTaskId[0]);

    return newTaskId[0];
  }
}
